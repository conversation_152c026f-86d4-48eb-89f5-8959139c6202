/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.file.merge.sftp.system;

import org.apache.seatunnel.connectors.seatunnel.file.merge.sftp.system.MinaSftpConnectionPool.MinaSftpConnection;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FSDataInputStream;
import org.apache.hadoop.fs.FSDataOutputStream;
import org.apache.hadoop.fs.FileStatus;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.fs.permission.FsPermission;
import org.apache.hadoop.util.Progressable;
import org.apache.sshd.sftp.client.SftpClient;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import lombok.extern.slf4j.Slf4j;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URI;
import java.net.URLDecoder;
import java.util.ArrayList;

/** SFTP FileSystem using Apache MINA SSHD. */
@Slf4j
public class SFTPFileSystem extends FileSystem {

    public static final Logger LOG = LoggerFactory.getLogger(SFTPFileSystem.class);

    private MinaSftpConnectionPool connectionPool;
    private URI uri;

    private static final int DEFAULT_SFTP_PORT = 22;
    public static final int DEFAULT_MAX_CONNECTION = 20; // MINA SSHD supports more connections
    public static final int DEFAULT_BUFFER_SIZE = 1024 * 1024;
    public static final int DEFAULT_BLOCK_SIZE = 4 * 1024;
    public static final String FS_SFTP_USER_PREFIX = "fs.sftp.user.";
    public static final String FS_SFTP_PASSWORD_PREFIX = "fs.sftp.password.";
    public static final String FS_SFTP_HOST = "fs.sftp.host";
    public static final String FS_SFTP_HOST_PORT = "fs.sftp.host.port";
    public static final String FS_SFTP_KEYFILE = "fs.sftp.keyfile";
    public static final String FS_SFTP_CONNECTION_MAX = "fs.sftp.connection.max";
    public static final String E_SAME_DIRECTORY_ONLY = "only same directory renames are supported";
    public static final String E_HOST_NULL = "Invalid host specified";
    public static final String E_USER_NULL =
            "No user specified for sftp connection. Expand URI or credential file.";
    public static final String E_PATH_DIR = "Path %s is a directory.";
    public static final String E_FILE_STATUS = "Failed to get file status";
    public static final String E_FILE_NOTFOUND = "File %s does not exist.";
    public static final String E_FILE_EXIST = "File already exists: %s";
    public static final String E_CREATE_DIR = "create(): Mkdirs failed to create: %s";
    public static final String E_DIR_CREATE_FROMFILE =
            "Can't make directory for path %s since it is a file.";
    public static final String E_MAKE_DIR_FORPATH =
            "Can't make directory for path \"%s\" under \"%s\".";
    public static final String E_DIR_NOTEMPTY = "Directory: %s is not empty.";
    public static final String E_FILE_CHECK_FAILED = "File check failed";
    public static final String E_NOT_SUPPORTED = "Not supported";
    public static final String E_SPATH_NOTEXIST = "Source path %s does not exist";
    public static final String E_DPATH_EXIST = "Destination path %s already exist, cannot rename!";
    public static final String E_FAILED_GETHOME = "Failed to get home directory";
    public static final String E_FAILED_DISCONNECT = "Failed to disconnect";

    private void setConfigurationFromURI(URI uriInfo, Configuration conf) throws IOException {

        // get host information from URI
        String host = uriInfo.getHost();
        host = (host == null) ? conf.get(FS_SFTP_HOST, null) : host;
        if (host == null) {
            throw new IOException(E_HOST_NULL);
        }
        conf.set(FS_SFTP_HOST, host);

        int port = uriInfo.getPort();
        port = (port == -1) ? conf.getInt(FS_SFTP_HOST_PORT, DEFAULT_SFTP_PORT) : port;
        conf.setInt(FS_SFTP_HOST_PORT, port);

        // get user/password information from URI
        String userAndPwdFromUri = uriInfo.getUserInfo();
        if (userAndPwdFromUri != null) {
            String[] userPasswdInfo = userAndPwdFromUri.split(":");
            String user = userPasswdInfo[0];
            user = URLDecoder.decode(user, "UTF-8");
            conf.set(FS_SFTP_USER_PREFIX + host, user);
            if (userPasswdInfo.length > 1) {
                conf.set(FS_SFTP_PASSWORD_PREFIX + host + "." + user, userPasswdInfo[1]);
            }
        }

        String user = conf.get(FS_SFTP_USER_PREFIX + host);
        if (user == null || user.equals("")) {
            throw new IllegalStateException(E_USER_NULL);
        }

        int connectionMax = conf.getInt(FS_SFTP_CONNECTION_MAX, DEFAULT_MAX_CONNECTION);
        connectionPool = new MinaSftpConnectionPool(connectionMax, connectionMax);
    }

    private MinaSftpConnection connect() throws IOException {
        Configuration conf = getConf();

        String host = conf.get(FS_SFTP_HOST, null);
        int port = conf.getInt(FS_SFTP_HOST_PORT, DEFAULT_SFTP_PORT);
        String user = conf.get(FS_SFTP_USER_PREFIX + host, null);
        String pwd = conf.get(FS_SFTP_PASSWORD_PREFIX + host + "." + user, null);
        String keyFile = conf.get(FS_SFTP_KEYFILE, null);

        return connectionPool.connect(host, port, user, pwd, keyFile);
    }

    private void disconnect(MinaSftpConnection connection) throws IOException {
        connectionPool.disconnect(connection);
    }

    private Path makeAbsolute(Path workDir, Path path) {
        if (path.isAbsolute()) {
            return path;
        }
        return new Path(workDir, path);
    }

    private boolean exists(MinaSftpConnection connection, Path file) throws IOException {
        try {
            getFileStatus(connection, file);
            return true;
        } catch (FileNotFoundException fnfe) {
            return false;
        } catch (IOException ioe) {
            throw new IOException(E_FILE_STATUS, ioe);
        }
    }

    /**
     * Convenience method, so that we don't open a new connection when using this method from within
     * another method. Otherwise every API invocation incurs the overhead of opening/closing a TCP
     * connection.
     */
    private FileStatus getFileStatus(MinaSftpConnection connection, Path file) throws IOException {
        FileStatus fileStat = null;
        SftpClient client = connection.getSftpClient();
        Path workDir;
        try {
            workDir = new Path(client.realPath("."));
        } catch (Exception e) {
            throw new IOException(e);
        }
        Path absolute = makeAbsolute(workDir, file);
        Path parentPath = absolute.getParent();
        if (parentPath == null) { // root directory
            long length = -1; // Length of root directory on server not known
            boolean isDir = true;
            int blockReplication = 1;
            long blockSize = DEFAULT_BLOCK_SIZE; // Block Size not known.
            long modTime = -1; // Modification time of root directory not known.
            Path root = new Path("/");
            return new FileStatus(
                    length,
                    isDir,
                    blockReplication,
                    blockSize,
                    modTime,
                    root.makeQualified(this.getUri(), this.getWorkingDirectory()));
        }
        String pathName = parentPath.toUri().getPath();
        Iterable<SftpClient.DirEntry> sftpFiles;
        try {
            sftpFiles = client.readDir(pathName);
        } catch (Exception e) {
            throw new FileNotFoundException(String.format(E_FILE_NOTFOUND, file));
        }
        if (sftpFiles != null) {
            for (SftpClient.DirEntry sftpFile : sftpFiles) {
                if (sftpFile.getFilename().equals(file.getName())) {
                    // file found in directory
                    fileStat = getFileStatus(connection, sftpFile, parentPath);
                    break;
                }
            }
            if (fileStat == null) {
                throw new FileNotFoundException(String.format(E_FILE_NOTFOUND, file));
            }
        } else {
            throw new FileNotFoundException(String.format(E_FILE_NOTFOUND, file));
        }
        return fileStat;
    }

    private FileStatus getFileStatus(
            MinaSftpConnection connection, SftpClient.DirEntry sftpFile, Path parentPath)
            throws IOException {

        SftpClient client = connection.getSftpClient();
        SftpClient.Attributes attr = sftpFile.getAttributes();
        long length = attr.getSize();
        boolean isDir = attr.isDirectory();
        boolean isLink = attr.isSymbolicLink();
        if (isLink) {
            String link = parentPath.toUri().getPath() + "/" + sftpFile.getFilename();
            try {
                link = client.realPath(link);

                Path linkParent = new Path("/", link);

                FileStatus fstat = getFileStatus(connection, linkParent);
                isDir = fstat.isDirectory();
                length = fstat.getLen();
            } catch (Exception e) {
                throw new IOException(e);
            }
        }
        int blockReplication = 1;
        // Using default block size since there is no way in SFTP channel to know of
        // block sizes on server. The assumption could be less than ideal.
        long blockSize = DEFAULT_BLOCK_SIZE;
        long modTime = attr.getModifyTime().toInstant().toEpochMilli();
        long accessTime = attr.getAccessTime().toInstant().toEpochMilli();
        FsPermission permission = getPermissions(attr);
        // not be able to get the real user group name, just use the user and group
        // id
        String user = Integer.toString(attr.getUserId());
        String group = Integer.toString(attr.getGroupId());
        Path filePath = new Path(parentPath, sftpFile.getFilename());

        return new FileStatus(
                length,
                isDir,
                blockReplication,
                blockSize,
                modTime,
                accessTime,
                permission,
                user,
                group,
                filePath.makeQualified(this.getUri(), this.getWorkingDirectory()));
    }

    private FsPermission getPermissions(SftpClient.Attributes attr) {
        return new FsPermission((short) attr.getPermissions());
    }

    /**
     * Convenience method, so that we don't open a new connection when using this method from within
     * another method. Otherwise every API invocation incurs the overhead of opening/closing a TCP
     * connection.
     */
    private boolean mkdirs(MinaSftpConnection connection, Path file, FsPermission permission)
            throws IOException {
        boolean created = true;
        SftpClient client = connection.getSftpClient();
        Path workDir;
        try {
            workDir = new Path(client.realPath("."));
        } catch (Exception e) {
            throw new IOException(e);
        }
        Path absolute = makeAbsolute(workDir, file);
        String pathName = absolute.getName();
        if (!exists(connection, absolute)) {
            Path parent = absolute.getParent();
            created = parent == null || mkdirs(connection, parent, FsPermission.getDefault());
            if (created) {
                String parentDir = parent.toUri().getPath();
                boolean succeeded = true;
                String errorPwd = "";
                try {
                    // 在MINA SSHD中，mkdir直接使用绝对路径
                    String fullPath =
                            parentDir.endsWith("/")
                                    ? parentDir + pathName
                                    : parentDir + "/" + pathName;
                    client.mkdir(fullPath);
                } catch (Exception e) {
                    // 再次 exists 检查，目录不存在或者是个同名文件再抛异常
                    if (!exists(connection, absolute) || isFile(connection, absolute)) {
                        throw new IOException(
                                String.format(E_MAKE_DIR_FORPATH, pathName, parentDir));
                    }
                    // 目录已存在且为目录，安全忽略
                }
                created = created & succeeded;
            }
        } else if (isFile(connection, absolute)) {
            throw new IOException(String.format(E_DIR_CREATE_FROMFILE, absolute));
        }
        return created;
    }

    private boolean isFile(MinaSftpConnection connection, Path file) throws IOException {
        try {
            return !getFileStatus(connection, file).isDirectory();
        } catch (FileNotFoundException e) {
            return false; // file does not exist
        } catch (IOException ioe) {
            throw new IOException(E_FILE_CHECK_FAILED, ioe);
        }
    }

    /**
     * Convenience method, so that we don't open a new connection when using this method from within
     * another method. Otherwise every API invocation incurs the overhead of opening/closing a TCP
     * connection.
     */
    private boolean delete(MinaSftpConnection connection, Path file, boolean recursive)
            throws IOException {
        SftpClient client = connection.getSftpClient();
        Path workDir;
        try {
            workDir = new Path(client.realPath("."));
        } catch (Exception e) {
            throw new IOException(e);
        }
        Path absolute = makeAbsolute(workDir, file);
        String pathName = absolute.toUri().getPath();
        FileStatus fileStat = null;
        try {
            fileStat = getFileStatus(connection, absolute);
        } catch (FileNotFoundException e) {
            // file not found, no need to delete, return true
            return false;
        }
        if (!fileStat.isDirectory()) {
            boolean status = true;
            try {
                client.remove(pathName);
            } catch (Exception e) {
                status = false;
            }
            return status;
        } else {
            boolean status = true;
            FileStatus[] dirEntries = listStatus(connection, absolute);
            if (dirEntries != null && dirEntries.length > 0) {
                if (!recursive) {
                    throw new IOException(String.format(E_DIR_NOTEMPTY, file));
                }
                for (int i = 0; i < dirEntries.length; ++i) {
                    delete(connection, new Path(absolute, dirEntries[i].getPath()), recursive);
                }
            }
            try {
                client.rmdir(pathName);
            } catch (Exception e) {
                status = false;
            }
            return status;
        }
    }

    /**
     * Convenience method, so that we don't open a new connection when using this method from within
     * another method. Otherwise every API invocation incurs the overhead of opening/closing a TCP
     * connection.
     */
    private FileStatus[] listStatus(MinaSftpConnection connection, Path file) throws IOException {
        SftpClient client = connection.getSftpClient();
        Path workDir;
        try {
            workDir = new Path(client.realPath("."));
        } catch (Exception e) {
            throw new IOException(e);
        }
        Path absolute = makeAbsolute(workDir, file);
        FileStatus fileStat = getFileStatus(connection, absolute);
        if (!fileStat.isDirectory()) {
            return new FileStatus[] {fileStat};
        }
        Iterable<SftpClient.DirEntry> sftpFiles;
        try {
            sftpFiles = client.readDir(absolute.toUri().getPath());
        } catch (Exception e) {
            throw new IOException(e);
        }
        ArrayList<FileStatus> fileStats = new ArrayList<FileStatus>();
        for (SftpClient.DirEntry entry : sftpFiles) {
            String fname = entry.getFilename();
            // skip current and parent directory, ie. "." and ".."
            if (!".".equalsIgnoreCase(fname) && !"..".equalsIgnoreCase(fname)) {
                fileStats.add(getFileStatus(connection, entry, absolute));
            }
        }
        return fileStats.toArray(new FileStatus[fileStats.size()]);
    }

    private boolean rename(MinaSftpConnection connection, Path src, Path dst) throws IOException {
        SftpClient client = connection.getSftpClient();
        Path workDir;
        try {
            workDir = new Path(client.realPath("."));
        } catch (Exception e) {
            throw new IOException(e);
        }
        Path absoluteSrc = makeAbsolute(workDir, src);
        Path absoluteDst = makeAbsolute(workDir, dst);

        if (!exists(connection, absoluteSrc)) {
            throw new IOException(String.format(E_SPATH_NOTEXIST, src));
        }
        if (exists(connection, absoluteDst)) {
            throw new IOException(String.format(E_DPATH_EXIST, dst));
        }
        boolean renamed = true;
        try {
            // 在MINA SSHD中，rename直接使用绝对路径
            client.rename(src.toUri().getPath(), dst.toUri().getPath());
        } catch (Exception e) {
            renamed = false;
        }
        return renamed;
    }

    @Override
    public void initialize(URI uriInfo, Configuration conf) throws IOException {
        super.initialize(uriInfo, conf);

        setConfigurationFromURI(uriInfo, conf);
        setConf(conf);
        this.uri = uriInfo;
    }

    @Override
    public String getScheme() {
        return "sftp";
    }

    @Override
    public URI getUri() {
        return uri;
    }

    @Override
    public FSDataInputStream open(Path f, int bufferSize) throws IOException {
        MinaSftpConnection connection = connect();
        SftpClient client = connection.getSftpClient();
        Path workDir;
        try {
            workDir = new Path(client.realPath("."));
        } catch (Exception e) {
            throw new IOException(e);
        }
        Path absolute = makeAbsolute(workDir, f);
        FileStatus fileStat = getFileStatus(connection, absolute);
        if (fileStat.isDirectory()) {
            disconnect(connection);
            throw new IOException(String.format(E_PATH_DIR, f));
        }
        InputStream is;
        try {
            // the path could be a symbolic link, so get the real path
            absolute = new Path("/", client.realPath(absolute.toUri().getPath()));

            // 在MINA SSHD中，直接使用InputStream
            is = client.read(absolute.toUri().getPath());
        } catch (Exception e) {
            throw new IOException(e);
        }

        FSDataInputStream fis =
                new FSDataInputStream(new SFTPInputStream(is, connection, statistics));
        return fis;
    }

    /**
     * A stream obtained via this call must be closed before using other APIs of this class or else
     * the invocation will block.
     */
    @Override
    public FSDataOutputStream create(
            Path f,
            FsPermission permission,
            boolean overwrite,
            int bufferSize,
            short replication,
            long blockSize,
            Progressable progress)
            throws IOException {
        final MinaSftpConnection connection = connect();
        SftpClient client = connection.getSftpClient();
        Path workDir;
        try {
            workDir = new Path(client.realPath("."));
        } catch (Exception e) {
            throw new IOException(e);
        }
        Path absolute = makeAbsolute(workDir, f);
        if (exists(connection, f)) {
            if (overwrite) {
                delete(connection, f, false);
            } else {
                disconnect(connection);
                throw new IOException(String.format(E_FILE_EXIST, f));
            }
        }
        Path parent = absolute.getParent();
        if (parent == null || !mkdirs(connection, parent, FsPermission.getDefault())) {
            parent = (parent == null) ? new Path("/") : parent;
            disconnect(connection);
            throw new IOException(String.format(E_CREATE_DIR, parent));
        }
        OutputStream os;
        try {
            // 在MINA SSHD中，直接使用OutputStream
            os = client.write(absolute.toUri().getPath());
        } catch (Exception e) {
            throw new IOException(e);
        }
        FSDataOutputStream fos =
                new FSDataOutputStream(os, statistics) {
                    @Override
                    public void close() throws IOException {
                        super.close();
                        disconnect(connection);
                    }
                };

        return fos;
    }

    @Override
    public FSDataOutputStream append(Path f, int bufferSize, Progressable progress)
            throws IOException {
        throw new IOException(E_NOT_SUPPORTED);
    }

    /*
     * The parent of source and destination can be different. It is suppose to
     * work like 'move'
     */
    @Override
    public boolean rename(Path src, Path dst) throws IOException {
        MinaSftpConnection connection = connect();
        try {
            boolean success = rename(connection, src, dst);
            return success;
        } finally {
            disconnect(connection);
        }
    }

    @Override
    public boolean delete(Path f, boolean recursive) throws IOException {
        MinaSftpConnection connection = connect();
        try {
            boolean success = delete(connection, f, recursive);
            return success;
        } finally {
            disconnect(connection);
        }
    }

    @Override
    public FileStatus[] listStatus(Path f) throws IOException {
        MinaSftpConnection connection = connect();
        try {
            FileStatus[] stats = listStatus(connection, f);
            return stats;
        } finally {
            disconnect(connection);
        }
    }

    @Override
    public void setWorkingDirectory(Path newDir) {
        // we do not maintain the working directory state
    }

    @Override
    public Path getWorkingDirectory() {
        // Return home directory always since we do not maintain state.
        return getHomeDirectory();
    }

    @Override
    public Path getHomeDirectory() {
        MinaSftpConnection connection = null;
        try {
            connection = connect();
            Path homeDir = new Path(connection.getSftpClient().canonicalPath("."));
            return homeDir;
        } catch (Exception ioe) {
            return null;
        } finally {
            try {
                disconnect(connection);
            } catch (IOException ioe) {
            }
        }
    }

    @Override
    public boolean mkdirs(Path f, FsPermission permission) throws IOException {
        MinaSftpConnection connection = connect();
        try {
            boolean success = mkdirs(connection, f, permission);
            return success;
        } finally {
            disconnect(connection);
        }
    }

    @Override
    public FileStatus getFileStatus(Path f) throws IOException {
        MinaSftpConnection connection = connect();
        try {
            FileStatus status = getFileStatus(connection, f);
            return status;
        } finally {
            disconnect(connection);
        }
    }

    @Override
    public void close() throws IOException {
        super.close();
        connectionPool.shutdown();
    }
}
