/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.file.merge.sftp.system;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;

import java.io.IOException;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Simple and reliable SFTP connection pool using JSch with performance optimizations This version
 * focuses on stability and performance without complex features
 */
public class SimpleSftpConnectionPool {

    public static final Logger LOG = LoggerFactory.getLogger(SimpleSftpConnectionPool.class);

    // Maximum number of allowed live connections
    private int maxConnection;
    private int liveConnectionCount;
    private final ConcurrentHashMap<ConnectionInfo, HashSet<SimpleSftpConnection>> idleConnections =
            new ConcurrentHashMap<ConnectionInfo, HashSet<SimpleSftpConnection>>();
    private final ConcurrentHashMap<SimpleSftpConnection, ConnectionInfo> con2infoMap =
            new ConcurrentHashMap<SimpleSftpConnection, ConnectionInfo>();

    // Thread-safe connection management
    private final Object poolLock = new Object();

    SimpleSftpConnectionPool(int maxConnection, int liveConnectionCount) {
        // 优化连接池配置以提高性能
        this.maxConnection = Math.max(maxConnection, 20); // 支持更多并发连接
        this.liveConnectionCount = Math.max(liveConnectionCount, 10);

        LOG.info(
                "SimpleSftpConnectionPool initialized with maxConnections={}, liveConnections={}",
                this.maxConnection,
                this.liveConnectionCount);
    }

    SimpleSftpConnection getFromPool(ConnectionInfo info) throws IOException {
        Set<SimpleSftpConnection> cons = idleConnections.get(info);
        SimpleSftpConnection connection;

        if (cons != null && cons.size() > 0) {
            Iterator<SimpleSftpConnection> it = cons.iterator();
            if (it.hasNext()) {
                connection = it.next();
                cons.remove(connection);
                if (cons.isEmpty()) {
                    idleConnections.remove(info);
                }
                LOG.debug("Reusing connection from pool for {}", info.getHost());
                return connection;
            }
        }
        return null;
    }

    void returnToPool(SimpleSftpConnection connection) {
        ConnectionInfo info = con2infoMap.get(connection);
        if (info != null) {
            HashSet<SimpleSftpConnection> cons =
                    idleConnections.computeIfAbsent(info, k -> new HashSet<>());
            cons.add(connection);
            LOG.debug("Returned connection to pool for {}", info.getHost());
        }
    }

    /** Shutdown the connection pool and close all open connections. */
    synchronized void shutdown() {
        LOG.info("Shutting down SimpleSftpConnectionPool, con2infoMap size=" + con2infoMap.size());

        this.maxConnection = 0;
        Set<SimpleSftpConnection> cons = con2infoMap.keySet();
        if (cons != null && cons.size() > 0) {
            Set<SimpleSftpConnection> copy = new HashSet<SimpleSftpConnection>(cons);
            for (SimpleSftpConnection con : copy) {
                try {
                    disconnect(con);
                } catch (IOException ioe) {
                    ConnectionInfo info = con2infoMap.get(con);
                    LOG.error(
                            "Error encountered while closing connection to " + info.getHost(), ioe);
                }
            }
        }
        this.idleConnections.clear();
        this.con2infoMap.clear();
    }

    public synchronized int getMaxConnection() {
        return maxConnection;
    }

    public synchronized void setMaxConnection(int maxConn) {
        this.maxConnection = maxConn;
    }

    public SimpleSftpConnection connect(
            String host, int port, String user, String password, String keyFile)
            throws IOException {

        // get connection from pool
        ConnectionInfo info = new ConnectionInfo(host, port, user);
        SimpleSftpConnection connection = getFromPool(info);

        if (connection != null) {
            if (connection.isConnected()) {
                return connection;
            } else {
                // 连接已断开，从池中移除
                synchronized (poolLock) {
                    --liveConnectionCount;
                    con2infoMap.remove(connection);
                    Set<SimpleSftpConnection> cons = idleConnections.get(info);
                    if (cons != null) {
                        cons.remove(connection);
                    }
                }
                connection = null;
            }
        }

        // create a new connection and add to pool
        try {
            JSch jsch = new JSch();

            // 性能优化配置
            if (keyFile != null && keyFile.length() > 0) {
                jsch.addIdentity(keyFile);
            }

            Session session = jsch.getSession(user, host, port);
            session.setPassword(password);

            // 关键性能优化配置
            java.util.Properties config = new java.util.Properties();
            config.put("StrictHostKeyChecking", "no"); // 跳过主机密钥检查
            config.put("compression.s2c", "none"); // 禁用压缩以提高性能
            config.put("compression.c2s", "none");
            config.put("MaxAuthTries", "1"); // 减少认证尝试次数
            config.put("tcp.nodelay", "true"); // 禁用Nagle算法
            config.put("server_alive_interval", "30000"); // 30秒保活间隔
            config.put("server_alive_count_max", "3");

            session.setConfig(config);
            session.setTimeout(30000); // 30秒超时
            session.connect();

            ChannelSftp channel = (ChannelSftp) session.openChannel("sftp");
            channel.connect();

            connection = new SimpleSftpConnection(session, channel);

            synchronized (poolLock) {
                con2infoMap.put(connection, info);
                liveConnectionCount++;
            }

            LOG.info(
                    "Created new Simple SFTP connection. Pool status: active={}, idle={}, max={}",
                    liveConnectionCount,
                    getIdleCount(),
                    maxConnection);

            return connection;

        } catch (Exception e) {
            throw new IOException("Failed to connect to SFTP server: " + e.getMessage(), e);
        }
    }

    void disconnect(SimpleSftpConnection connection) throws IOException {
        if (connection != null) {
            boolean closeConnection = false;
            synchronized (poolLock) {
                if (liveConnectionCount > maxConnection) {
                    --liveConnectionCount;
                    con2infoMap.remove(connection);
                    closeConnection = true;
                }
            }
            if (closeConnection) {
                if (connection.isConnected()) {
                    try {
                        connection.close();
                    } catch (Exception e) {
                        throw new IOException("Failed to close SFTP connection", e);
                    }
                }
            } else {
                returnToPool(connection);
            }
        }
    }

    public int getIdleCount() {
        return this.idleConnections.size();
    }

    public int getLiveConnCount() {
        return this.liveConnectionCount;
    }

    public int getConnPoolSize() {
        return this.con2infoMap.size();
    }

    /**
     * Class to capture the minimal set of information that distinguish between different
     * connections.
     */
    static class ConnectionInfo {
        private String host = "";
        private int port;
        private String user = "";

        ConnectionInfo(String hst, int prt, String usr) {
            this.host = hst;
            this.port = prt;
            this.user = usr;
        }

        public String getHost() {
            return host;
        }

        public void setHost(String hst) {
            this.host = hst;
        }

        public int getPort() {
            return port;
        }

        public void setPort(int prt) {
            this.port = prt;
        }

        public String getUser() {
            return user;
        }

        public void setUser(String usr) {
            this.user = usr;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }

            if (obj instanceof ConnectionInfo) {
                ConnectionInfo con = (ConnectionInfo) obj;

                boolean ret = true;
                if (this.host == null || !this.host.equalsIgnoreCase(con.host)) {
                    ret = false;
                }
                if (this.port >= 0 && this.port != con.port) {
                    ret = false;
                }
                if (this.user == null || !this.user.equalsIgnoreCase(con.user)) {
                    ret = false;
                }
                return ret;
            } else {
                return false;
            }
        }

        @Override
        public int hashCode() {
            int hashCode = 0;
            if (host != null) {
                hashCode += host.hashCode();
            }
            hashCode += port;
            if (user != null) {
                hashCode += user.hashCode();
            }
            return hashCode;
        }
    }

    /** Wrapper class for JSch Session and ChannelSftp with optimized performance */
    public static class SimpleSftpConnection {
        private final Session session;
        private final ChannelSftp channel;

        public SimpleSftpConnection(Session session, ChannelSftp channel) {
            this.session = session;
            this.channel = channel;
        }

        public Session getSession() {
            return session;
        }

        public ChannelSftp getChannel() {
            return channel;
        }

        public boolean isConnected() {
            return session.isConnected() && channel.isConnected();
        }

        public void close() throws IOException {
            try {
                if (channel != null && channel.isConnected()) {
                    channel.disconnect();
                }
            } finally {
                if (session != null && session.isConnected()) {
                    session.disconnect();
                }
            }
        }
    }
}
