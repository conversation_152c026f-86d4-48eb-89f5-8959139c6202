#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  job.mode = "BATCH"

  # You can set spark configuration here
  spark.master = local
}

source {
  FakeSource {
    row.num = 5
    string.fake.mode = "template"
    string.template = ["tyrantlucifer", "hailin", "kris", "fanjia", "zongwen", "gaojun"]
    tinyint.fake.mode = "template"
    tinyint.template = [1, 2, 3, 4, 5, 6, 7, 8, 9]
    smalling.fake.mode = "template"
    smallint.template = [10, 11, 12, 13, 14, 15, 16, 17, 18, 19]
    int.fake.mode = "template"
    int.template = [20, 21, 22, 23, 24, 25, 26, 27, 28, 29]
    bigint.fake.mode = "template"
    bigint.template = [30, 31, 32, 33, 34, 35, 36, 37, 38, 39]
    float.fake.mode = "template"
    float.template = [40.0, 41.0, 42.0, 43.0]
    double.fake.mode = "template"
    double.template = [44.0, 45.0, 46.0, 47.0]
    schema {
      fields {
        c_string = string
        c_tinyint = tinyint
        c_smallint = smallint
        c_int = int
        c_bigint = bigint
        c_float = float
        c_double = double
      }
    }
  }
}

sink {
  Assert {
    rules {
      rule_ruls = [
        {
          rule_type = MAX_ROW
          rule_value = 5
        }
      ],
      field_rules = [
        {
          field_name = c_string
          field_type = string
          field_value = [
            {
              rule_type = MIN_LENGTH
              rule_value = 4
            },
            {
              rule_type = MAX_LENGTH
              rule_value = 13
            }
          ]
        },
        {
          field_name = c_tinyint
          field_type = tinyint
          field_value = [
            {
              rule_type = MIN
              rule_value = 1
            },
            {
              rule_type = MAX
              rule_value = 9
            }
          ]
        },
        {
          field_name = c_smallint
          field_type = smallint
          field_value = [
            {
              rule_type = MIN
              rule_value = 10
            },
            {
              rule_type = MAX
              rule_value = 19
            }
          ]
        },
        {
          field_name = c_int
          field_type = int
          field_value = [
            {
              rule_type = MIN
              rule_value = 20
            },
            {
              rule_type = MAX
              rule_value = 29
            }
          ]
        },
        {
          field_name = c_bigint
          field_type = bigint
          field_value = [
            {
              rule_type = MIN
              rule_value = 30
            },
            {
              rule_type = MAX
              rule_value = 39
            }
          ]
        },
        {
          field_name = c_float
          field_type = float
          field_value = [
            {
              rule_type = MIN
              rule_value = 40
            },
            {
              rule_type = MAX
              rule_value = 43
            }
          ]
        },
        {
          field_name = c_double
          field_type = double
          field_value = [
            {
              rule_type = MIN
              rule_value = 44
            },
            {
              rule_type = MAX
              rule_value = 47
            }
          ]
        }
      ]
    }
  }
}