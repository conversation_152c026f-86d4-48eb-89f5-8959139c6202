#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  job.mode = "BATCH"
}

source {
  FakeSource {
    tables_configs = [
      {
        row.num = 100
        schema = {
          table = "test.table1"
          columns = [
            {
                name = id
                type = bigint
            }
            {
                name = name
                type = string
            }
            {
                name = age
                type = int
            }
          ]
          primaryKey = {
            name = "primary key"
            columnNames = ["id"]
          }
          constraintKeys = [
              {
                  constraintName = "unique_name"
                  constraintType = UNIQUE_KEY
                  constraintColumns = [
                      {
                          columnName = "id"
                          sortType = ASC
                      }
                  ]
              }
          ]
        }
      },
      {
          row.num = 100
          schema = {
            table = "test.table2"
            columns = [
              {
                  name = id
                  type = bigint
              }
              {
                  name = name
                  type = string
              }
              {
                  name = age
                  type = int
              }
            ]
            primaryKey = {
              name = "primary key"
              columnNames = ["id"]
            }
            constraintKeys = [
                {
                    constraintName = "unique_name"
                    constraintType = UNIQUE_KEY
                    constraintColumns = [
                        {
                            columnName = "id"
                            sortType = ASC
                        }
                    ]
                }
            ]
          }
        }
    ]
    result_table_name = "fake"
  }
}

sink{
  Assert {
      rules {
        table-names = ["test.table1", "test.table2"]
      }
    }
}