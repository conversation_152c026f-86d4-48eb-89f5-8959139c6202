#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  job.mode = "BATCH"

  # You can set spark configuration here
  spark.master = local
}

source {
  FakeSource {
    row.num = 5
    string.template = ["tyrantlucifer", "hailin", "kris", "fanjia", "zongwen", "gaojun"]
    tinyint.min = 1
    tinyint.max = 9
    smallint.min = 10
    smallint.max = 19
    int.min = 20
    int.max = 29
    bigint.min = 30
    bigint.max = 39
    float.min = 40.0
    float.max = 43.0
    double.min = 44.0
    double.max = 47.0
    schema {
      fields {
        c_string = string
        c_tinyint = tinyint
        c_smallint = smallint
        c_int = int
        c_bigint = bigint
        c_float = float
        c_double = double
      }
    }
  }
}

sink {
  Assert {
    rules {
      rule_ruls = [
        {
          rule_type = MAX_ROW
          rule_value = 5
        }
      ],
      field_rules = [
        {
          field_name = c_string
          field_type = string
          field_value = [
            {
              rule_type = MIN_LENGTH
              rule_value = 4
            },
            {
              rule_type = MAX_LENGTH
              rule_value = 13
            }
          ]
        },
        {
          field_name = c_tinyint
          field_type = tinyint
          field_value = [
            {
              rule_type = MIN
              rule_value = 1
            },
            {
              rule_type = MAX
              rule_value = 9
            }
          ]
        },
        {
          field_name = c_smallint
          field_type = smallint
          field_value = [
            {
              rule_type = MIN
              rule_value = 10
            },
            {
              rule_type = MAX
              rule_value = 19
            }
          ]
        },
        {
          field_name = c_int
          field_type = int
          field_value = [
            {
              rule_type = MIN
              rule_value = 20
            },
            {
              rule_type = MAX
              rule_value = 29
            }
          ]
        },
        {
          field_name = c_bigint
          field_type = bigint
          field_value = [
            {
              rule_type = MIN
              rule_value = 30
            },
            {
              rule_type = MAX
              rule_value = 39
            }
          ]
        },
        {
          field_name = c_float
          field_type = float
          field_value = [
            {
              rule_type = MIN
              rule_value = 40
            },
            {
              rule_type = MAX
              rule_value = 43
            }
          ]
        },
        {
          field_name = c_double
          field_type = double
          field_value = [
            {
              rule_type = MIN
              rule_value = 44
            },
            {
              rule_type = MAX
              rule_value = 47
            }
          ]
        }
      ]
    }
  }
}