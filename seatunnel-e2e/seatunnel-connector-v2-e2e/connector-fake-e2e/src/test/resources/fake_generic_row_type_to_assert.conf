#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  job.mode = BATCH
  # checkpoint.interval = 10000
}

source {
  FakeSource {
    row.num = 1
    schema = {
      fields {
        c_0 = "map<string, {c_int=int\nc_string=string}>"
        c_1 = "map<string, {c_int=int,c_string=string}>"
        c_2 = "map<string, {c_int=int,c_string=string,c_row={c_int=int}}>"
        c_3 = "map<string, {\"c_int\":\"int\",\"c_string\":\"string\"}>"
      }
    }
    result_table_name = "fake"
  }
}

sink{
  Assert {
    source_table_name = "fake"
    rules =
      {
        catalog_table_rule {
          column_rule = [
            {
              name = "c_0"
              type = "map<string, {c_int=int\nc_string=string}>"
            }
            {
              name = "c_1"
              type = "map<string, {c_int=int,c_string=string}>"
            }
            {
              name = "c_2"
              type = "map<string, {c_int=int,c_string=string,c_row={c_int=int}}>"
            }
            {
              name = "c_3"
              type = "map<string, {\"c_int\":\"int\",\"c_string\":\"string\"}>"
            }
          ]
        }
      }
  }
}