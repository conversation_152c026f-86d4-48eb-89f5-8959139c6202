#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  job.mode = "BATCH"
}

source{
    jdbc {
        url = "jdbc:snowflake://<account_id>.aws.snowflakecomputing.com"
        driver = "net.snowflake.client.jdbc.SnowflakeDriver"
        user = "user"
        password = "password"
        query = """
        SELECT
          ID,
          NUM,
          DEC,
          INT,
          BIGINT,
          SMALLINT,
          TINYINT,
          BYTEINT,
          FLOAT,
          DOUBL<PERSON>,
          VARCHAR_COL,
          CHAR_COL,
          STRING_COL,
          BOOLEAN_COL,
          DATE_COL,
          TIME_COL,
          TIMESTAMP_COL,
          TIMESTAMP_NTZ_COL,
          TIMESTAMP_LTZ_COL,
          TIMESTAMP_TZ_COL,
          VARIANT_COL,
          OBJECT_COL,
          GEOGRAPHY_COL,
          GEOMETRY_COL,
          BINARY_COL,
          VARBINARY_COL
        FROM TEST_INPUT_DB.TEST_INPUT_SCHEMA.MOCK_DATA;
        """
    }
}

transform {
}

sink {
  jdbc {
          url = "jdbc:snowflake://<account_id>.snowflakecomputing.com"
          driver = "net.snowflake.client.jdbc.SnowflakeDriver"
          user = "user"
          password = "password"
          query = """
          INSERT INTO TEST_INPUT_DB.TEST_INPUT_SCHEMA.MOCK_DATA (id, num, dec, int, bigint, smallint, tinyint, byteint, float, double, varchar_col, char_col, string_col, boolean_col, date_col, time_col, timestamp_col, timestamp_ntz_col, timestamp_ltz_col, timestamp_tz_col, variant_col, object_col, geography_col, geometry_col, binary_col, varbinary_col)
          values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
          """
      }
}
