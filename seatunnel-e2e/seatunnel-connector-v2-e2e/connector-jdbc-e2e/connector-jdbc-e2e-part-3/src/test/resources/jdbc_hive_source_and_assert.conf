#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  # You can set engine configuration here
  parallelism = 1
  job.mode = "BATCH"
}

source {
  # This is a example source plugin **only for test and demonstrate the feature source plugin**
    Jdbc {
        url = "**************************************"
        user = "root"
        driver = "org.apache.hive.jdbc.HiveDriver"
        query = "select * from hive_e2e_source_table"
        auto_commit= false
    }
}

transform {
}

sink{
  assert {
    rules =
      {
        row_rules = [
          {
            rule_type = MAX_ROW
            rule_value = 3
          },
          {
            rule_type = MIN_ROW
            rule_value = 3
          }
        ],
        field_rules = [
        {
          field_name = hive_e2e_source_table.int_column
          field_type = int
          field_value = [{equals_to = 2}]
        },
        {
          field_name = hive_e2e_source_table.integer_column
          field_type = int
          field_value = [{equals_to = 1}]
        },
        {
          field_name = hive_e2e_source_table.bigint_column
          field_type = bigint
          field_value = [{equals_to = 1234567890}]
        },
        {
          field_name = hive_e2e_source_table.smallint_column
          field_type = smallint
          field_value = [{equals_to = 32767}]
         },
        {
          field_name = hive_e2e_source_table.tinyint_column
          field_type = tinyint
          field_value = [{equals_to = 127}]
         },
        {
          field_name = hive_e2e_source_table.double_column
          field_type = double
          field_value = [{equals_to = 123.45}]
          },
        {
          field_name = hive_e2e_source_table.double_precision_column
          field_type = double
          field_value = [{equals_to = 123.45}]
          },
        {
          field_name = hive_e2e_source_table.float_column
          field_type = float
          field_value = [{equals_to = 67.89}]
          },
        {
          field_name = hive_e2e_source_table.string_column
          field_type = string
          field_value = [{equals_to = "Hello, Hive"}]
          },
        {
          field_name = hive_e2e_source_table.char_column
          field_type = string
          field_value = [{equals_to = "CharCol   "}]
          },
        {
          field_name = hive_e2e_source_table.varchar_column
          field_type = string
          field_value = [{equals_to = "VarcharCol"}]
          },
        {
          field_name = hive_e2e_source_table.boolean_column
          field_type = boolean
          field_value = [{equals_to = "TRUE"}]
          },
        {
          field_name = hive_e2e_source_table.date_column
          field_type = date
          field_value = [{equals_to = "2023-09-04"}]
          },
        {
          field_name = hive_e2e_source_table.timestamp_column
          field_type = timestamp
          field_value = [{equals_to = "2023-09-04T10:30:00"}]
          },
        {
          field_name = hive_e2e_source_table.decimal_column
          field_type = "decimal(10,2)"
          field_value = [{equals_to = "42.10"}]
          },
        {
         field_name = hive_e2e_source_table.numeric_column
         field_type = "decimal(10,2)"
         field_value = [{equals_to = 42.12}]
         },
        ]
      }
  }
}
