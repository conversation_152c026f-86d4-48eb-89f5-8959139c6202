#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  job.mode = "BATCH"
}

source {
  Jdbc {
    url = "jdbc:dm://e2e_dmdb_upset:5236"
    driver = "dm.jdbc.driver.DmDriver"
    connection_check_timeout_sec = 1000
    user = "SYSDBA2"
    password = "testPassword"
    query = "select * from SYSDBA2.E2E_TABLE_SOURCE_UPSET"
  }

}

sink {
  Jdbc {
    url = "jdbc:dm://e2e_dmdb_upset:5236"
    driver = "dm.jdbc.driver.DmDriver"
    connection_check_timeout_sec = 1000
    user = "SYSDBA2"
    password = "testPassword"
    database = "DAMENG"
    primary_keys = ["DM_BIT"]
    table = "SYSDBA2.E2E_TABLE_SINK_UPSET"
    generate_sink_sql = true
    query = ""
  }
}

