#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  job.mode = "BATCH"
}

source {
  # This is a example source plugin **only for test and demonstrate the feature source plugin**
  Jdbc {
    driver = com.ibm.db2.jcc.DB2Driver
    url = "****************************"
    user = "db2inst1"
    password = "123456"
    query = """
    select * from "E2E".SOURCE;
    """
  }

  # If you would like to get more information about how to configure seatunnel and see full list of source plugins,
  # please go to https://seatunnel.apache.org/docs/connector-v2/source/Jdbc
}

sink {
  Jdbc {
    driver = com.ibm.db2.jcc.DB2Driver
    url = "****************************"
    user = "db2inst1"
    password = "123456"
    query = """
insert into "E2E".SINK (C_BOOLEAN, C_SMALLINT, C_INT, C_INTEGER, C_BIGINT, C_DECIMAL, C_DEC, C_NUMERIC, C_NUM, C_REAL, C_FLOAT, C_DOUBLE, C_DOUBLE_PRECISION, C_CHAR, C_VARCHAR, C_BINARY, C_VARBINARY, C_DATE)
 values ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
"""
  }

  # If you would like to get more information about how to configure seatunnel and see full list of sink plugins,
  # please go to https://seatunnel.apache.org/docs/connector-v2/sink/Jdbc
}
