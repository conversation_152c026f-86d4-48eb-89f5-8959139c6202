#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  job.mode = "BATCH"
}

source {
  jdbc {
    url = "*************************************"
    driver = "com.mysql.cj.jdbc.Driver"
    connection_check_timeout_sec = 100
    user = "root"
    password = "Abc!@#135_seatunnel"

    table_list = [
      {
        table_path = "source.table1"
      },
      {
        table_path = "source.table2"
        query = "select * from source.table2"
      }
    ]
    where_condition = "where c_int >= 0"
    split.size = 8096
    split.even-distribution.factor.upper-bound = 100
    split.even-distribution.factor.lower-bound = 0.05
    split.sample-sharding.threshold = 1000
    split.inverse-sampling.rate = 1000
  }
}

sink {
  jdbc {
    url = "*************************************"
    driver = "com.mysql.cj.jdbc.Driver"
    user = "root"
    password = "Abc!@#135_seatunnel"

    database = "sink"
    generate_sink_sql = true
  }
}
