#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  job.mode = "BATCH"
}

source{
    jdbc{
        url = "******************************************************"
        driver = "org.postgresql.Driver"
        user = "test"
        password = "test"
        query ="""select gid, text_col, varchar_col, char_col, boolean_col, smallint_col, integer_col, bigint_col, decimal_col, numeric_col, real_col, double_precision_col,
                         smallserial_col, serial_col, bigserial_col, date_col, timestamp_col, bpchar_col, age, name, point, linestring, polygon_colums, multipoint,
                         multilinestring, multipolygon, geometrycollection, geog from pg_ide_source_table"""
    }
}


sink {
  Jdbc {
    driver = org.postgresql.Driver
    url = "******************************************************"
    user = test
    password = test
    generate_sink_sql = true
    field_ide = UPPERCASE
    database = test
    table = "public.PG_IDE_SINK_TABLE"
    primary_keys = ["gid"]
  }
}