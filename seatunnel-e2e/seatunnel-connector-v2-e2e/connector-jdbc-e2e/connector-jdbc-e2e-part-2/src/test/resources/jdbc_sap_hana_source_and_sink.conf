#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  job.mode = "BATCH"
}

source {
  Jdbc {
    url = "****************************"
    driver = "com.sap.db.jdbc.Driver"
    connection_check_timeout_sec = 1000
    user = "SYSDBA"
    password = "SYSDBA"
    query = """select * from E2E_TEST.E2E_TABLE_SOURCE"""
  }

}

transform {
}

sink {
  Jdbc {
    url = "****************************"
    driver = "com.sap.db.jdbc.Driver"
    connection_check_timeout_sec = 1000
    user = "SYSDBA"
    password = "SYSDBA"
    query = """
INSERT INTO E2E_TEST.E2E_TABLE_SOURCE (BLOB_VAL, VARBINARY_VAL, DATE_VAL, TIME_VAL, LONGDATE_VAL, SECONDDATE_VAL,
                                   TIMESTAMP_VAL, DECIMAL_VAL, REAL_VAL, SMALLDECIMAL_VAL, BIGINT_VAL, INTEGER_VAL,
                                   SMALLINT_VAL, TINYINT_VAL, DOUBLE_VAL, CLOB_VAL, NCLOB_VAL, TEXT_VAL, ALPHANUM_VAL,
                                   NVARCHAR_VAL, SHORTTEXT_VAL, VARCHAR_VAL)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
"""
  }
}

