#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  job.mode = "BATCH"
}

source {
  # This is a example source plugin **only for test and demonstrate the feature source plugin**
  Jdbc {
    driver = com.teradata.jdbc.TeraDriver
    url = "*************************************,DATABASE=test,TYPE=FASTEXPORT"
    user = "dbc"
    password = "dbc"
    query = """
    select id,
    c_byteint,
    c_smallint,
    c_integer,
    c_bigint,
    c_float,
    c_decimal,
    c_char,
    c_varchar,
    c_byte,
    c_varbyte,
    c_date,
    c_timestamp
    from source_table;
    """
  }
  # If you would like to get more information about how to configure seatunnel and see full list of source plugins,
  # please go to https://seatunnel.apache.org/docs/connector-v2/source/Jdbc
}

sink {
  Jdbc {
    driver = com.teradata.jdbc.TeraDriver
    url = "*************************************,DATABASE=test,TYPE=FASTLOAD"
    user = "dbc"
    password = "dbc"
    auto_commit = false
    query = """
    insert into sink_table(id,
                           c_byteint,
                           c_smallint,
                           c_integer,
                           c_bigint,
                           c_float,
                           c_decimal,
                           c_char,
                           c_varchar,
                           c_byte,
                           c_varbyte,
                           c_date,
                           c_timestamp)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
"""
  }
  # If you would like to get more information about how to configure seatunnel and see full list of sink plugins,
  # please go to https://seatunnel.apache.org/docs/connector-v2/sink/Jdbc
}
