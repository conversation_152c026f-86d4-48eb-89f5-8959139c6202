#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  job.mode = "BATCH"
}

source {
  jdbc {
    url = "****************************************************"
    driver = "com.mysql.cj.jdbc.Driver"
    connection_check_timeout_sec = 100
    user = "root"
    password = "Abc!@#135_seatunnel"
    query = "select * from source;"
  }
}

transform {
}

sink {
  jdbc {
    url = "****************************************************"
    driver = "com.mysql.cj.jdbc.Driver"
    user = "root"
    password = "Abc!@#135_seatunnel"
    generate_sink_sql = true
    table = "test_laowang"
    database = "seatunnel"
    schema_save_mode = "CREATE_SCHEMA_WHEN_NOT_EXIST"
    data_save_mode="APPEND_DATA"
  }
}
