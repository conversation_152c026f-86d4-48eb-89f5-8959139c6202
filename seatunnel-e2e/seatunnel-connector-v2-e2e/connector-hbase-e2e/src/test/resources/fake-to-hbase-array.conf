#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  execution.parallelism = 1
  job.mode = "BATCH"
}

source {
  FakeSource {
      schema = {
          fields {
              name = string
              score = int
              c_array_string = "array<string>"
              c_array_int = "array<int>"
          }
      }
      rows = [
          {
              kind = INSERT
              fields = ["A", 100,["a","b","c"],[1,2,3]]
          },
          {
              kind = INSERT
              fields = ["B", 200,["d","e","f"],[4,5,6]]
          },
          {
              kind = INSERT
              fields = ["C", 300,["g","h","k"],[7,8,9]]
          }
      ]
  }
}

sink {
  Hbase {
    zookeeper_quorum = "hbase-e2e:2181"
    table = "seatunnel_test"
    rowkey_column = ["name"]
    family_name {
      all_columns = info
    }
  }
}