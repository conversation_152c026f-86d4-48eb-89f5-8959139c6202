<?xml version="1.0" encoding="UTF-8"?>
<!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at
       http://www.apache.org/licenses/LICENSE-2.0
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.apache.seatunnel</groupId>
        <artifactId>seatunnel-e2e</artifactId>
        <version>2.3.4</version>
    </parent>

    <artifactId>seatunnel-connector-v2-e2e</artifactId>
    <packaging>pom</packaging>
    <name>SeaTunnel : E2E : Connector V2 :</name>

    <modules>
        <module>connector-assert-e2e</module>
        <module>connector-jdbc-e2e</module>
        <module>connector-redis-e2e</module>
        <module>connector-cdc-sqlserver-e2e</module>
        <module>connector-clickhouse-e2e</module>
        <module>connector-starrocks-e2e</module>
        <module>connector-influxdb-e2e</module>
        <module>connector-amazondynamodb-e2e</module>
        <module>connector-amazonsqs-e2e</module>
        <module>connector-file-local-e2e</module>
        <module>connector-file-cos-e2e</module>
        <module>connector-file-sftp-e2e</module>
        <module>connector-file-oss-e2e</module>
        <module>connector-cassandra-e2e</module>
        <module>connector-neo4j-e2e</module>
        <module>connector-http-e2e</module>
        <module>connector-rabbitmq-e2e</module>
        <module>connector-kafka-e2e</module>
        <module>connector-doris-e2e</module>
        <module>connector-fake-e2e</module>
        <module>connector-elasticsearch-e2e</module>
        <module>connector-iotdb-e2e</module>
        <module>connector-cdc-mysql-e2e</module>
        <module>connector-cdc-mongodb-e2e</module>
        <module>connector-iceberg-e2e</module>
        <module>connector-iceberg-hadoop3-e2e</module>
        <module>connector-tdengine-e2e</module>
        <module>connector-datahub-e2e</module>
        <module>connector-mongodb-e2e</module>
        <module>connector-hbase-e2e</module>
        <module>connector-maxcompute-e2e</module>
        <module>connector-google-firestore-e2e</module>
        <module>connector-rocketmq-e2e</module>
        <module>connector-file-ftp-e2e</module>
        <module>connector-pulsar-e2e</module>
        <module>connector-paimon-e2e</module>
        <module>connector-kudu-e2e</module>
        <module>connector-cdc-postgres-e2e</module>
        <module>connector-cdc-oracle-e2e</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>seatunnel-e2e-common</artifactId>
            <version>${project.version}</version>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>seatunnel-flink-13-starter</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>seatunnel-flink-15-starter</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>seatunnel-spark-2-starter</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>seatunnel-spark-3-starter</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>seatunnel-starter</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>
