#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  job.mode = "BATCH"
}

source {
  MySQL-CDC {
      parallelism = 1
      server-id = 5656
      username = "root"
      password = "Bigdata2023@"
      table-names = ["test.e2e_table_sink"]
      base-url = "jdbc:mysql://*************:56725/test"
    }
}

sink {
  Doris {
    fenodes = "***********:8234"
    username = root
    password = ""
    database = "test"
    table = "e2e_table_sink"
    sink.label-prefix = "test-cdc"
    sink.enable-2pc = "false"
    sink.enable-delete = "true"
    doris.config {
      format = "csv"
      "column_separator" = "\\x01"
      "line_delimiter" = "\\x01"
    }
  }
}