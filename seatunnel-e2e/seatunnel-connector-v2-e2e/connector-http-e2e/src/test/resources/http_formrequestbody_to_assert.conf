#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  job.mode = "BATCH"
}

source {
  Http {
    result_table_name = "http"
    url = "http://mockserver:1080/example/formBody"
    method = "POST"
    params ={id = 1}
    format = "json"
    schema = {
      fields {
        name = string
        age = int
      }
    }
  }
}

sink {
  Assert {
    source_table_name = "http"
    rules {
     row_rules = [
            {
              rule_type = MAX_ROW
              rule_value = 2
            },
            {
              rule_type = MIN_ROW
              rule_value = 2
            }
          ],
          field_rules = [
            {
              field_name = name
              field_type = string
              field_value = [
                {
                  rule_type = NOT_NULL
                }
              ]
            },
            {
              field_name = age
              field_type = int
              field_value = [
                {
                  rule_type = NOT_NULL
                }
              ]
            }
           ]
    }
  }
   Http {
        source_table_name = "http"
        url = "http://mockserver:1080/example/webhook"
        headers {
            token = "9e32e859ef044462a257e1fc76730066"
        }
    }
}