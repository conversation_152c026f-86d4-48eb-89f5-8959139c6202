// https://www.mock-server.com/mock_server/getting_started.html#request_matchers

[
  {
    "httpRequest": {
      "method" : "GET",
      "path": "/example/http"
    },
    "httpResponse": {
      "body": [
        {
          "c_map":{
            "ccQcS":"PrhhP",
            "ypJZu":"MsOdX",
            "YFBJW":"iPXGR",
            "ipjwT":"kcgPQ",
            "EpKKR":"jgRfX"
          },
          "c_array":[
            887776100,
            1633238485,
            1009033208,
            600614572,
            1487972145
          ],
          "c_string":"WArEB",
          "c_boolean":false,
          "c_tinyint":-90,
          "c_smallint":15920,
          "c_int":1127427935,
          "c_bigint":4712806879122100224,
          "c_float":162047600000000000000000000000000000000,
          "c_double":27509088104078520000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000,
          "c_bytes":"Q3NrVnQ=",
          "c_date":"2022-04-27",
          "c_decimal":88574263949141714798.835853182708550244,
          "c_timestamp":"2022-01-26T17:39:00",
          "c_row":{
            "C_MAP":{
              "IVaKD":"bydeV",
              "CnKBd":"kcZdt",
              "RGlmG":"XuMyE",
              "krSIr":"FPeal",
              "IfhvE":"ReKxo"
            },
            "C_ARRAY":[
              86555282,
              967939739,
              1162972923,
              1662468723,
              546056811
            ],
            "C_STRING":"bYjyZ",
            "C_BOOLEAN":false,
            "C_TINYINT":-121,
            "C_SMALLINT":29252,
            "C_INT":977226449,
            "C_BIGINT":5047232039582494720,
            "C_FLOAT":253456430000000000000000000000000000000,
            "C_DOUBLE":158834248299979960000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000,
            "C_BYTES":"TEVLTHU=",
            "C_DATE":"2022-04-25",
            "C_DECIMAL":55295207715324162970.316560703127334413,
            "C_TIMESTAMP":"2022-06-14T23:03:00"
          }
        },
        {
          "c_map":{
            "AKiQx":"wIIdk",
            "zgunZ":"qvHRy",
            "ohVQL":"WfBPo",
            "EzUcN":"yPhVF",
            "qusBc":"FWbcI"
          },
          "c_array":[
            1837821269,
            980724530,
            2085935679,
            386596035,
            1433416218
          ],
          "c_string":"LGMAw",
          "c_boolean":false,
          "c_tinyint":-65,
          "c_smallint":25802,
          "c_int":1312064317,
          "c_bigint":4434124023629949952,
          "c_float":101861250000000000000000000000000000000,
          "c_double":30746920457833206000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000,
          "c_bytes":"V2pjem4=",
          "c_date":"2022-04-21",
          "c_decimal":1943815605574160687.499688237951975681,
          "c_timestamp":"2022-08-09T09:32:00",
          "c_row":{
            "C_MAP":{
              "qMdUz":"ylcLM",
              "bcwFI":"qgkJT",
              "lrPiD":"JRdjf",
              "zmRix":"uqOKy",
              "NEHDJ":"tzJbU"
            },
            "C_ARRAY":[
              951883741,
              2012849301,
              1709478035,
              1095210330,
              94263648
            ],
            "C_STRING":"VAdKg",
            "C_BOOLEAN":true,
            "C_TINYINT":-121,
            "C_SMALLINT":24543,
            "C_INT":1853224936,
            "C_BIGINT":6511613165105889280,
            "C_FLOAT":248867480000000000000000000000000000000,
            "C_DOUBLE":167553012802413800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000,
            "C_BYTES":"UnNlRXo=",
            "C_DATE":"2022-01-26",
            "C_DECIMAL":50854841532374241314.109746688054104586,
            "C_TIMESTAMP":"2022-02-18T22:33:00"
          }
        },
        {
          "c_map":{
            "VLlqs":"OwUpp",
            "MWXek":"KDEYD",
            "RAZII":"zGJSJ",
            "wjBNl":"IPTvu",
            "YkGPS":"ORquf"
          },
          "c_array":[
            1530393427,
            2055877022,
            1389865473,
            926021483,
            402841214
          ],
          "c_string":"TNcNF",
          "c_boolean":false,
          "c_tinyint":-93,
          "c_smallint":26429,
          "c_int":1890712921,
          "c_bigint":78884499049828080,
          "c_float":78168420000000000000000000000000000000,
          "c_double":78525745220115830000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000,
          "c_bytes":"cHhzZVA=",
          "c_date":"2022-06-05",
          "c_decimal":32486229951636021942.906126821535443395,
          "c_timestamp":"2022-04-09T16:03:00",
          "c_row":{
            "C_MAP":{
              "yIfRN":"gTBEL",
              "oUnIJ":"GtmSz",
              "IGuwP":"TyCOu",
              "BwTUT":"HgnUn",
              "MFrOg":"csTeq"
            },
            "C_ARRAY":[
              306983370,
              1604264996,
              2038631670,
              265692923,
              717846839
            ],
            "C_STRING":"wavDf",
            "C_BOOLEAN":true,
            "C_TINYINT":-48,
            "C_SMALLINT":29740,
            "C_INT":1691565731,
            "C_BIGINT":6162480816264462336,
            "C_FLOAT":332183420000000000000000000000000000000,
            "C_DOUBLE":99936669025917730000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000,
            "C_BYTES":"RnVoR0Q=",
            "C_DATE":"2022-04-09",
            "C_DECIMAL":81349181592680914623.14214231545254843,
            "C_TIMESTAMP":"2022-11-06T02:58:00"
          }
        },
        {
          "c_map":{
            "OSHIu":"FlSum",
            "MaSwp":"KYQkK",
            "iXmjf":"zlkgq",
            "jOBeN":"RDfwI",
            "mNmag":"QyxeW"
          },
          "c_array":[
            1632475346,
            1988402914,
            1222138765,
            1952120146,
            1223582179
          ],
          "c_string":"fUmcz",
          "c_boolean":false,
          "c_tinyint":86,
          "c_smallint":2122,
          "c_int":798530029,
          "c_bigint":4622710207120546816,
          "c_float":274385260000000000000000000000000000000,
          "c_double":3710018378162975000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000,
          "c_bytes":"WWlCdWk=",
          "c_date":"2022-10-08",
          "c_decimal":21195432655142738238.345609599825344131,
          "c_timestamp":"2022-01-12T10:58:00",
          "c_row":{
            "C_MAP":{
              "HdaHZ":"KMWIb",
              "ETTGr":"zDkTq",
              "kdTfa":"AyDqd",
              "beLSj":"gCVdP",
              "RDgtj":"YhJcx"
            },
            "C_ARRAY":[
              1665702810,
              2138839494,
              2129312562,
              1248002085,
              1536850903
            ],
            "C_STRING":"jJotn",
            "C_BOOLEAN":false,
            "C_TINYINT":90,
            "C_SMALLINT":5092,
            "C_INT":543799429,
            "C_BIGINT":3526775209703891968,
            "C_FLOAT":19285203000000000000000000000000000000,
            "C_DOUBLE":119569847888769830000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000,
            "C_BYTES":"RVd4a1g=",
            "C_DATE":"2022-09-19",
            "C_DECIMAL":86909407361565847023.835229924753629936,
            "C_TIMESTAMP":"2022-09-15T18:06:00"
          }
        },
        {
          "c_map":{
            "aDAzK":"sMIOi",
            "NSyDX":"TKSoT",
            "JLxhC":"NpeWZ",
            "LAjup":"KmHDA",
            "HUIPE":"yAOKq"
          },
          "c_array":[
            1046349188,
            1243865078,
            849372657,
            522012053,
            644827083
          ],
          "c_string":"pwRSn",
          "c_boolean":true,
          "c_tinyint":55,
          "c_smallint":14285,
          "c_int":290002708,
          "c_bigint":4717741595193431040,
          "c_float":309654730000000000000000000000000000000,
          "c_double":129844722952577660000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000,
          "c_bytes":"TE1oUWg=",
          "c_date":"2022-05-05",
          "c_decimal":75406296065465000885.249652183329686608,
          "c_timestamp":"2022-07-05T14:40:00",
          "c_row":{
            "C_MAP":{
              "WTqxL":"RuJsv",
              "UXnhR":"HOjTp",
              "EeFOQ":"PSpGy",
              "YtxFI":"ACjTB",
              "YAlWV":"NlOjQ"
            },
            "C_ARRAY":[
              1610325348,
              1432388472,
              557306114,
              590115029,
              1704913966
            ],
            "C_STRING":"Pnkxe",
            "C_BOOLEAN":false,
            "C_TINYINT":-15,
            "C_SMALLINT":8909,
            "C_INT":2084130154,
            "C_BIGINT":3344333580258222592,
            "C_FLOAT":333064730000000000000000000000000000000,
            "C_DOUBLE":92331438173921840000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000,
            "C_BYTES":"enpuUXk=",
            "C_DATE":"2022-07-01",
            "C_DECIMAL":87998983887293909887.925694693860636437,
            "C_TIMESTAMP":"2022-02-12T07:45:00"
          }
        }
      ],
      "headers": {
        "Content-Type": "application/json"
      }
    }
  },
  {
    "httpRequest": {
      "method" : "GET",
      "path": "/example/httpMultiLine"
    },
    "httpResponse": {
      "body": "{\"age\":22,\"name\":\"Jone\",\"salary\":1000} \r\n {\"age\":24,\"name\":\"vieech\",\"salary\":3000}",
      "headers": {
        "Content-Type": "application/json"
      }
    }
  },
  {
    "httpRequest": {
      "secure": true,
      "method" : "GET",
      "path": "/example/https"
    },
    "httpResponse": {
      "body": [
        {
          "name": "1",
          "age": 18
        },
        {
          "name": "2",
          "age": 19
        }
      ],
      "headers": {
        "Content-Type": "application/json"
      }
    }
  },
  {
    "httpRequest": {
      "method" : "GET",
      "path": "/example/page",
      "queryStringParameters": {
        "pn": "1"
      }
    },
    "httpResponse": {
      "body": [
        {
          "name": "1",
          "age": 18
        },
        {
          "name": "2",
          "age": 19
        }
      ],
      "headers": {
        "Content-Type": "application/json"
      }
    }
  },
  {
    "httpRequest": {
      "method" : "GET",
      "path": "/example/page",
      "queryStringParameters": {
        "pn": "2"
      }
    },
    "httpResponse": {
      "body": [
        {
          "name": "1",
          "age": 18
        },
        {
          "name": "2",
          "age": 19
        }
      ],
      "headers": {
        "Content-Type": "application/json"
      }
    }
  },
  {
    "httpRequest": {
      "method" : "GET",
      "path": "/contentjson/mock"
    },
    "httpResponse": {
      "body": {
        "store": {
          "book": [
            {
              "category": "reference",
              "author": "Nigel Rees",
              "title": "Sayings of the Century",
              "price": 8.95
            },
            {
              "category": "fiction",
              "author": "Evelyn Waugh",
              "title": "Sword of Honour",
              "price": 12.99
            },
            {
              "category": "fiction",
              "author": "Herman Melville",
              "title": "Moby Dick",
              "isbn": "0-553-21311-3",
              "price": 8.99
            },
            {
              "category": "fiction",
              "author": "J. R. R. Tolkien",
              "title": "The Lord of the Rings",
              "isbn": "0-395-19395-8",
              "price": 22.99
            }
          ],
          "bicycle": {
            "color": "red",
            "price": 19.95
          }
        },
        "expensive": 10
      },
      "headers": {
        "Content-Type": "application/json"
      }
    }
  },
  {
    "httpRequest": {
      "method": "GET",
      "path": "/orgs/apache/repos"
    },
    "httpResponse": {
      "body": [
        {
          "id": 160986,
          "node_id": "MDEwOlJlcG9zaXRvcnkxNjA5ODY=",
          "name": "tapestry3",
          "full_name": "apache/tapestry3",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/tapestry3",
          "description": "Mirror of Apache Tapestry 3",
          "fork": false,
          "url": "https://api.github.com/repos/apache/tapestry3",
          "forks_url": "https://api.github.com/repos/apache/tapestry3/forks",
          "keys_url": "https://api.github.com/repos/apache/tapestry3/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/tapestry3/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/tapestry3/teams",
          "hooks_url": "https://api.github.com/repos/apache/tapestry3/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/tapestry3/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/tapestry3/events",
          "assignees_url": "https://api.github.com/repos/apache/tapestry3/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/tapestry3/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/tapestry3/tags",
          "blobs_url": "https://api.github.com/repos/apache/tapestry3/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/tapestry3/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/tapestry3/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/tapestry3/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/tapestry3/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/tapestry3/languages",
          "stargazers_url": "https://api.github.com/repos/apache/tapestry3/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/tapestry3/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/tapestry3/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/tapestry3/subscription",
          "commits_url": "https://api.github.com/repos/apache/tapestry3/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/tapestry3/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/tapestry3/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/tapestry3/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/tapestry3/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/tapestry3/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/tapestry3/merges",
          "archive_url": "https://api.github.com/repos/apache/tapestry3/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/tapestry3/downloads",
          "issues_url": "https://api.github.com/repos/apache/tapestry3/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/tapestry3/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/tapestry3/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/tapestry3/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/tapestry3/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/tapestry3/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/tapestry3/deployments",
          "created_at": "2009-03-27T15:41:52Z",
          "updated_at": "2022-12-16T06:12:47Z",
          "pushed_at": "2022-10-03T22:40:04Z",
          "git_url": "git://github.com/apache/tapestry3.git",
          "ssh_url": "**************:apache/tapestry3.git",
          "clone_url": "https://github.com/apache/tapestry3.git",
          "svn_url": "https://github.com/apache/tapestry3",
          "homepage": null,
          "size": 54936,
          "stargazers_count": 3,
          "watchers_count": 3,
          "language": "Java",
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 13,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 4,
          "license": null,
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "java",
            "tapestry",
            "web-framework"
          ],
          "visibility": "public",
          "forks": 13,
          "open_issues": 4,
          "watchers": 3,
          "default_branch": "trunk",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 160988,
          "node_id": "MDEwOlJlcG9zaXRvcnkxNjA5ODg=",
          "name": "apr-iconv",
          "full_name": "apache/apr-iconv",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/apr-iconv",
          "description": "Mirror of Apache Portable Runtime iconv",
          "fork": false,
          "url": "https://api.github.com/repos/apache/apr-iconv",
          "forks_url": "https://api.github.com/repos/apache/apr-iconv/forks",
          "keys_url": "https://api.github.com/repos/apache/apr-iconv/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/apr-iconv/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/apr-iconv/teams",
          "hooks_url": "https://api.github.com/repos/apache/apr-iconv/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/apr-iconv/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/apr-iconv/events",
          "assignees_url": "https://api.github.com/repos/apache/apr-iconv/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/apr-iconv/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/apr-iconv/tags",
          "blobs_url": "https://api.github.com/repos/apache/apr-iconv/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/apr-iconv/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/apr-iconv/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/apr-iconv/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/apr-iconv/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/apr-iconv/languages",
          "stargazers_url": "https://api.github.com/repos/apache/apr-iconv/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/apr-iconv/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/apr-iconv/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/apr-iconv/subscription",
          "commits_url": "https://api.github.com/repos/apache/apr-iconv/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/apr-iconv/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/apr-iconv/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/apr-iconv/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/apr-iconv/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/apr-iconv/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/apr-iconv/merges",
          "archive_url": "https://api.github.com/repos/apache/apr-iconv/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/apr-iconv/downloads",
          "issues_url": "https://api.github.com/repos/apache/apr-iconv/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/apr-iconv/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/apr-iconv/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/apr-iconv/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/apr-iconv/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/apr-iconv/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/apr-iconv/deployments",
          "created_at": "2009-03-27T15:41:52Z",
          "updated_at": "2022-10-06T00:11:25Z",
          "pushed_at": "2019-01-01T11:45:15Z",
          "git_url": "git://github.com/apache/apr-iconv.git",
          "ssh_url": "**************:apache/apr-iconv.git",
          "clone_url": "https://github.com/apache/apr-iconv.git",
          "svn_url": "https://github.com/apache/apr-iconv",
          "homepage": null,
          "size": 2539,
          "stargazers_count": 17,
          "watchers_count": 17,
          "language": "C",
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 18,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 1,
          "license": {
            "key": "apache-2.0",
            "name": "Apache License 2.0",
            "spdx_id": "Apache-2.0",
            "url": "https://api.github.com/licenses/apache-2.0",
            "node_id": "MDc6TGljZW5zZTI="
          },
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "apr"
          ],
          "visibility": "public",
          "forks": 18,
          "open_issues": 1,
          "watchers": 17,
          "default_branch": "trunk",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 160989,
          "node_id": "MDEwOlJlcG9zaXRvcnkxNjA5ODk=",
          "name": "tapestry4",
          "full_name": "apache/tapestry4",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/tapestry4",
          "description": "Mirror of Apache Tapestry 4",
          "fork": false,
          "url": "https://api.github.com/repos/apache/tapestry4",
          "forks_url": "https://api.github.com/repos/apache/tapestry4/forks",
          "keys_url": "https://api.github.com/repos/apache/tapestry4/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/tapestry4/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/tapestry4/teams",
          "hooks_url": "https://api.github.com/repos/apache/tapestry4/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/tapestry4/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/tapestry4/events",
          "assignees_url": "https://api.github.com/repos/apache/tapestry4/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/tapestry4/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/tapestry4/tags",
          "blobs_url": "https://api.github.com/repos/apache/tapestry4/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/tapestry4/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/tapestry4/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/tapestry4/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/tapestry4/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/tapestry4/languages",
          "stargazers_url": "https://api.github.com/repos/apache/tapestry4/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/tapestry4/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/tapestry4/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/tapestry4/subscription",
          "commits_url": "https://api.github.com/repos/apache/tapestry4/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/tapestry4/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/tapestry4/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/tapestry4/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/tapestry4/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/tapestry4/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/tapestry4/merges",
          "archive_url": "https://api.github.com/repos/apache/tapestry4/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/tapestry4/downloads",
          "issues_url": "https://api.github.com/repos/apache/tapestry4/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/tapestry4/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/tapestry4/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/tapestry4/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/tapestry4/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/tapestry4/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/tapestry4/deployments",
          "created_at": "2009-03-27T15:41:53Z",
          "updated_at": "2022-11-28T16:04:48Z",
          "pushed_at": "2022-04-05T04:43:10Z",
          "git_url": "git://github.com/apache/tapestry4.git",
          "ssh_url": "**************:apache/tapestry4.git",
          "clone_url": "https://github.com/apache/tapestry4.git",
          "svn_url": "https://github.com/apache/tapestry4",
          "homepage": null,
          "size": 76605,
          "stargazers_count": 6,
          "watchers_count": 6,
          "language": "Java",
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 13,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 8,
          "license": {
            "key": "other",
            "name": "Other",
            "spdx_id": "NOASSERTION",
            "url": null,
            "node_id": "MDc6TGljZW5zZTA="
          },
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "java",
            "tapestry",
            "web-framework"
          ],
          "visibility": "public",
          "forks": 13,
          "open_issues": 8,
          "watchers": 6,
          "default_branch": "trunk",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 160994,
          "node_id": "MDEwOlJlcG9zaXRvcnkxNjA5OTQ=",
          "name": "sling-old-svn-mirror",
          "full_name": "apache/sling-old-svn-mirror",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/sling-old-svn-mirror",
          "description": "Mirror of Apache Sling",
          "fork": false,
          "url": "https://api.github.com/repos/apache/sling-old-svn-mirror",
          "forks_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/forks",
          "keys_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/teams",
          "hooks_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/events",
          "assignees_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/tags",
          "blobs_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/languages",
          "stargazers_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/subscription",
          "commits_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/merges",
          "archive_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/downloads",
          "issues_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/sling-old-svn-mirror/deployments",
          "created_at": "2009-03-27T15:41:54Z",
          "updated_at": "2023-01-13T08:00:56Z",
          "pushed_at": "2018-06-29T19:44:29Z",
          "git_url": "git://github.com/apache/sling-old-svn-mirror.git",
          "ssh_url": "**************:apache/sling-old-svn-mirror.git",
          "clone_url": "https://github.com/apache/sling-old-svn-mirror.git",
          "svn_url": "https://github.com/apache/sling-old-svn-mirror",
          "homepage": "",
          "size": 86054,
          "stargazers_count": 218,
          "watchers_count": 218,
          "language": null,
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 265,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 0,
          "license": null,
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "java",
            "sling"
          ],
          "visibility": "public",
          "forks": 265,
          "open_issues": 0,
          "watchers": 218,
          "default_branch": "archived",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 160995,
          "node_id": "MDEwOlJlcG9zaXRvcnkxNjA5OTU=",
          "name": "xalan-j",
          "full_name": "apache/xalan-j",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/xalan-j",
          "description": "Mirror of Apache Xalan Java",
          "fork": false,
          "url": "https://api.github.com/repos/apache/xalan-j",
          "forks_url": "https://api.github.com/repos/apache/xalan-j/forks",
          "keys_url": "https://api.github.com/repos/apache/xalan-j/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/xalan-j/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/xalan-j/teams",
          "hooks_url": "https://api.github.com/repos/apache/xalan-j/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/xalan-j/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/xalan-j/events",
          "assignees_url": "https://api.github.com/repos/apache/xalan-j/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/xalan-j/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/xalan-j/tags",
          "blobs_url": "https://api.github.com/repos/apache/xalan-j/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/xalan-j/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/xalan-j/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/xalan-j/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/xalan-j/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/xalan-j/languages",
          "stargazers_url": "https://api.github.com/repos/apache/xalan-j/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/xalan-j/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/xalan-j/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/xalan-j/subscription",
          "commits_url": "https://api.github.com/repos/apache/xalan-j/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/xalan-j/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/xalan-j/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/xalan-j/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/xalan-j/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/xalan-j/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/xalan-j/merges",
          "archive_url": "https://api.github.com/repos/apache/xalan-j/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/xalan-j/downloads",
          "issues_url": "https://api.github.com/repos/apache/xalan-j/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/xalan-j/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/xalan-j/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/xalan-j/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/xalan-j/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/xalan-j/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/xalan-j/deployments",
          "created_at": "2009-03-27T15:41:55Z",
          "updated_at": "2023-01-19T05:25:23Z",
          "pushed_at": "2022-10-24T18:27:46Z",
          "git_url": "git://github.com/apache/xalan-j.git",
          "ssh_url": "**************:apache/xalan-j.git",
          "clone_url": "https://github.com/apache/xalan-j.git",
          "svn_url": "https://github.com/apache/xalan-j",
          "homepage": null,
          "size": 55092,
          "stargazers_count": 24,
          "watchers_count": 24,
          "language": "Java",
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 69,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 4,
          "license": {
            "key": "other",
            "name": "Other",
            "spdx_id": "NOASSERTION",
            "url": null,
            "node_id": "MDc6TGljZW5zZTA="
          },
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "xalan"
          ],
          "visibility": "public",
          "forks": 69,
          "open_issues": 4,
          "watchers": 24,
          "default_branch": "trunk",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 160996,
          "node_id": "MDEwOlJlcG9zaXRvcnkxNjA5OTY=",
          "name": "etch",
          "full_name": "apache/etch",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/etch",
          "description": "Mirror of Apache Etch",
          "fork": false,
          "url": "https://api.github.com/repos/apache/etch",
          "forks_url": "https://api.github.com/repos/apache/etch/forks",
          "keys_url": "https://api.github.com/repos/apache/etch/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/etch/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/etch/teams",
          "hooks_url": "https://api.github.com/repos/apache/etch/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/etch/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/etch/events",
          "assignees_url": "https://api.github.com/repos/apache/etch/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/etch/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/etch/tags",
          "blobs_url": "https://api.github.com/repos/apache/etch/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/etch/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/etch/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/etch/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/etch/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/etch/languages",
          "stargazers_url": "https://api.github.com/repos/apache/etch/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/etch/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/etch/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/etch/subscription",
          "commits_url": "https://api.github.com/repos/apache/etch/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/etch/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/etch/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/etch/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/etch/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/etch/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/etch/merges",
          "archive_url": "https://api.github.com/repos/apache/etch/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/etch/downloads",
          "issues_url": "https://api.github.com/repos/apache/etch/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/etch/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/etch/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/etch/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/etch/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/etch/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/etch/deployments",
          "created_at": "2009-03-27T15:41:55Z",
          "updated_at": "2022-11-28T16:04:48Z",
          "pushed_at": "2017-04-28T20:27:41Z",
          "git_url": "git://github.com/apache/etch.git",
          "ssh_url": "**************:apache/etch.git",
          "clone_url": "https://github.com/apache/etch.git",
          "svn_url": "https://github.com/apache/etch",
          "homepage": "",
          "size": 13740,
          "stargazers_count": 17,
          "watchers_count": 17,
          "language": "Java",
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 10,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 0,
          "license": {
            "key": "apache-2.0",
            "name": "Apache License 2.0",
            "spdx_id": "Apache-2.0",
            "url": "https://api.github.com/licenses/apache-2.0",
            "node_id": "MDc6TGljZW5zZTI="
          },
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "etch"
          ],
          "visibility": "public",
          "forks": 10,
          "open_issues": 0,
          "watchers": 17,
          "default_branch": "trunk",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 160997,
          "node_id": "MDEwOlJlcG9zaXRvcnkxNjA5OTc=",
          "name": "apr",
          "full_name": "apache/apr",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/apr",
          "description": "Mirror of Apache Portable Runtime",
          "fork": false,
          "url": "https://api.github.com/repos/apache/apr",
          "forks_url": "https://api.github.com/repos/apache/apr/forks",
          "keys_url": "https://api.github.com/repos/apache/apr/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/apr/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/apr/teams",
          "hooks_url": "https://api.github.com/repos/apache/apr/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/apr/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/apr/events",
          "assignees_url": "https://api.github.com/repos/apache/apr/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/apr/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/apr/tags",
          "blobs_url": "https://api.github.com/repos/apache/apr/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/apr/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/apr/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/apr/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/apr/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/apr/languages",
          "stargazers_url": "https://api.github.com/repos/apache/apr/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/apr/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/apr/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/apr/subscription",
          "commits_url": "https://api.github.com/repos/apache/apr/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/apr/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/apr/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/apr/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/apr/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/apr/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/apr/merges",
          "archive_url": "https://api.github.com/repos/apache/apr/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/apr/downloads",
          "issues_url": "https://api.github.com/repos/apache/apr/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/apr/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/apr/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/apr/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/apr/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/apr/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/apr/deployments",
          "created_at": "2009-03-27T15:41:55Z",
          "updated_at": "2023-02-07T07:30:31Z",
          "pushed_at": "2023-02-15T13:18:18Z",
          "git_url": "git://github.com/apache/apr.git",
          "ssh_url": "**************:apache/apr.git",
          "clone_url": "https://github.com/apache/apr.git",
          "svn_url": "https://github.com/apache/apr",
          "homepage": null,
          "size": 19609,
          "stargazers_count": 384,
          "watchers_count": 384,
          "language": "C",
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 185,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 9,
          "license": {
            "key": "apache-2.0",
            "name": "Apache License 2.0",
            "spdx_id": "Apache-2.0",
            "url": "https://api.github.com/licenses/apache-2.0",
            "node_id": "MDc6TGljZW5zZTI="
          },
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "apr"
          ],
          "visibility": "public",
          "forks": 185,
          "open_issues": 9,
          "watchers": 384,
          "default_branch": "trunk",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 160998,
          "node_id": "MDEwOlJlcG9zaXRvcnkxNjA5OTg=",
          "name": "stdcxx",
          "full_name": "apache/stdcxx",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/stdcxx",
          "description": "Mirror of Apache C++ Standard Library",
          "fork": false,
          "url": "https://api.github.com/repos/apache/stdcxx",
          "forks_url": "https://api.github.com/repos/apache/stdcxx/forks",
          "keys_url": "https://api.github.com/repos/apache/stdcxx/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/stdcxx/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/stdcxx/teams",
          "hooks_url": "https://api.github.com/repos/apache/stdcxx/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/stdcxx/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/stdcxx/events",
          "assignees_url": "https://api.github.com/repos/apache/stdcxx/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/stdcxx/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/stdcxx/tags",
          "blobs_url": "https://api.github.com/repos/apache/stdcxx/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/stdcxx/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/stdcxx/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/stdcxx/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/stdcxx/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/stdcxx/languages",
          "stargazers_url": "https://api.github.com/repos/apache/stdcxx/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/stdcxx/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/stdcxx/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/stdcxx/subscription",
          "commits_url": "https://api.github.com/repos/apache/stdcxx/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/stdcxx/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/stdcxx/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/stdcxx/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/stdcxx/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/stdcxx/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/stdcxx/merges",
          "archive_url": "https://api.github.com/repos/apache/stdcxx/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/stdcxx/downloads",
          "issues_url": "https://api.github.com/repos/apache/stdcxx/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/stdcxx/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/stdcxx/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/stdcxx/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/stdcxx/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/stdcxx/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/stdcxx/deployments",
          "created_at": "2009-03-27T15:41:56Z",
          "updated_at": "2022-10-06T00:11:24Z",
          "pushed_at": "2018-12-10T20:51:50Z",
          "git_url": "git://github.com/apache/stdcxx.git",
          "ssh_url": "**************:apache/stdcxx.git",
          "clone_url": "https://github.com/apache/stdcxx.git",
          "svn_url": "https://github.com/apache/stdcxx",
          "homepage": null,
          "size": 15270,
          "stargazers_count": 56,
          "watchers_count": 56,
          "language": "C++",
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 29,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 0,
          "license": {
            "key": "apache-2.0",
            "name": "Apache License 2.0",
            "spdx_id": "Apache-2.0",
            "url": "https://api.github.com/licenses/apache-2.0",
            "node_id": "MDc6TGljZW5zZTI="
          },
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "stdcxx"
          ],
          "visibility": "public",
          "forks": 29,
          "open_issues": 0,
          "watchers": 56,
          "default_branch": "trunk",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 160999,
          "node_id": "MDEwOlJlcG9zaXRvcnkxNjA5OTk=",
          "name": "zookeeper",
          "full_name": "apache/zookeeper",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/zookeeper",
          "description": "Apache ZooKeeper",
          "fork": false,
          "url": "https://api.github.com/repos/apache/zookeeper",
          "forks_url": "https://api.github.com/repos/apache/zookeeper/forks",
          "keys_url": "https://api.github.com/repos/apache/zookeeper/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/zookeeper/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/zookeeper/teams",
          "hooks_url": "https://api.github.com/repos/apache/zookeeper/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/zookeeper/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/zookeeper/events",
          "assignees_url": "https://api.github.com/repos/apache/zookeeper/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/zookeeper/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/zookeeper/tags",
          "blobs_url": "https://api.github.com/repos/apache/zookeeper/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/zookeeper/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/zookeeper/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/zookeeper/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/zookeeper/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/zookeeper/languages",
          "stargazers_url": "https://api.github.com/repos/apache/zookeeper/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/zookeeper/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/zookeeper/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/zookeeper/subscription",
          "commits_url": "https://api.github.com/repos/apache/zookeeper/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/zookeeper/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/zookeeper/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/zookeeper/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/zookeeper/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/zookeeper/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/zookeeper/merges",
          "archive_url": "https://api.github.com/repos/apache/zookeeper/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/zookeeper/downloads",
          "issues_url": "https://api.github.com/repos/apache/zookeeper/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/zookeeper/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/zookeeper/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/zookeeper/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/zookeeper/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/zookeeper/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/zookeeper/deployments",
          "created_at": "2009-03-27T15:41:56Z",
          "updated_at": "2023-02-17T05:31:18Z",
          "pushed_at": "2023-02-17T06:20:43Z",
          "git_url": "git://github.com/apache/zookeeper.git",
          "ssh_url": "**************:apache/zookeeper.git",
          "clone_url": "https://github.com/apache/zookeeper.git",
          "svn_url": "https://github.com/apache/zookeeper",
          "homepage": "https://zookeeper.apache.org",
          "size": 137053,
          "stargazers_count": 11064,
          "watchers_count": 11064,
          "language": "Java",
          "has_issues": false,
          "has_projects": false,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 6904,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 227,
          "license": {
            "key": "apache-2.0",
            "name": "Apache License 2.0",
            "spdx_id": "Apache-2.0",
            "url": "https://api.github.com/licenses/apache-2.0",
            "node_id": "MDc6TGljZW5zZTI="
          },
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "apache",
            "configuration-management",
            "consensus",
            "coordination",
            "database",
            "distributed-configuration",
            "distributed-database",
            "distributed-systems",
            "hacktoberfest",
            "java",
            "key-value",
            "service-discovery",
            "zab",
            "zookeeper"
          ],
          "visibility": "public",
          "forks": 6904,
          "open_issues": 227,
          "watchers": 11064,
          "default_branch": "master",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 161001,
          "node_id": "MDEwOlJlcG9zaXRvcnkxNjEwMDE=",
          "name": "lucenenet",
          "full_name": "apache/lucenenet",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/lucenenet",
          "description": "Apache Lucene.NET",
          "fork": false,
          "url": "https://api.github.com/repos/apache/lucenenet",
          "forks_url": "https://api.github.com/repos/apache/lucenenet/forks",
          "keys_url": "https://api.github.com/repos/apache/lucenenet/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/lucenenet/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/lucenenet/teams",
          "hooks_url": "https://api.github.com/repos/apache/lucenenet/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/lucenenet/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/lucenenet/events",
          "assignees_url": "https://api.github.com/repos/apache/lucenenet/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/lucenenet/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/lucenenet/tags",
          "blobs_url": "https://api.github.com/repos/apache/lucenenet/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/lucenenet/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/lucenenet/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/lucenenet/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/lucenenet/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/lucenenet/languages",
          "stargazers_url": "https://api.github.com/repos/apache/lucenenet/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/lucenenet/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/lucenenet/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/lucenenet/subscription",
          "commits_url": "https://api.github.com/repos/apache/lucenenet/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/lucenenet/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/lucenenet/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/lucenenet/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/lucenenet/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/lucenenet/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/lucenenet/merges",
          "archive_url": "https://api.github.com/repos/apache/lucenenet/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/lucenenet/downloads",
          "issues_url": "https://api.github.com/repos/apache/lucenenet/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/lucenenet/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/lucenenet/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/lucenenet/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/lucenenet/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/lucenenet/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/lucenenet/deployments",
          "created_at": "2009-03-27T15:41:57Z",
          "updated_at": "2023-02-14T11:33:00Z",
          "pushed_at": "2023-02-01T19:21:35Z",
          "git_url": "git://github.com/apache/lucenenet.git",
          "ssh_url": "**************:apache/lucenenet.git",
          "clone_url": "https://github.com/apache/lucenenet.git",
          "svn_url": "https://github.com/apache/lucenenet",
          "homepage": "https://lucenenet.apache.org/",
          "size": 174369,
          "stargazers_count": 1940,
          "watchers_count": 1940,
          "language": "C#",
          "has_issues": true,
          "has_projects": false,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 621,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 74,
          "license": {
            "key": "apache-2.0",
            "name": "Apache License 2.0",
            "spdx_id": "Apache-2.0",
            "url": "https://api.github.com/licenses/apache-2.0",
            "node_id": "MDc6TGljZW5zZTI="
          },
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "analysis",
            "apache",
            "hacktoberfest",
            "index",
            "information",
            "lucene",
            "lucenenet",
            "query",
            "retrieval",
            "search",
            "text"
          ],
          "visibility": "public",
          "forks": 621,
          "open_issues": 74,
          "watchers": 1940,
          "default_branch": "master",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 161004,
          "node_id": "MDEwOlJlcG9zaXRvcnkxNjEwMDQ=",
          "name": "apr-util",
          "full_name": "apache/apr-util",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/apr-util",
          "description": "Mirror of Apache Portable Runtime util",
          "fork": false,
          "url": "https://api.github.com/repos/apache/apr-util",
          "forks_url": "https://api.github.com/repos/apache/apr-util/forks",
          "keys_url": "https://api.github.com/repos/apache/apr-util/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/apr-util/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/apr-util/teams",
          "hooks_url": "https://api.github.com/repos/apache/apr-util/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/apr-util/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/apr-util/events",
          "assignees_url": "https://api.github.com/repos/apache/apr-util/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/apr-util/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/apr-util/tags",
          "blobs_url": "https://api.github.com/repos/apache/apr-util/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/apr-util/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/apr-util/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/apr-util/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/apr-util/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/apr-util/languages",
          "stargazers_url": "https://api.github.com/repos/apache/apr-util/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/apr-util/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/apr-util/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/apr-util/subscription",
          "commits_url": "https://api.github.com/repos/apache/apr-util/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/apr-util/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/apr-util/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/apr-util/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/apr-util/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/apr-util/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/apr-util/merges",
          "archive_url": "https://api.github.com/repos/apache/apr-util/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/apr-util/downloads",
          "issues_url": "https://api.github.com/repos/apache/apr-util/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/apr-util/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/apr-util/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/apr-util/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/apr-util/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/apr-util/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/apr-util/deployments",
          "created_at": "2009-03-27T15:41:58Z",
          "updated_at": "2022-12-09T16:33:33Z",
          "pushed_at": "2023-02-03T16:36:20Z",
          "git_url": "git://github.com/apache/apr-util.git",
          "ssh_url": "**************:apache/apr-util.git",
          "clone_url": "https://github.com/apache/apr-util.git",
          "svn_url": "https://github.com/apache/apr-util",
          "homepage": null,
          "size": 8300,
          "stargazers_count": 66,
          "watchers_count": 66,
          "language": null,
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 56,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 1,
          "license": null,
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "apr"
          ],
          "visibility": "public",
          "forks": 56,
          "open_issues": 1,
          "watchers": 66,
          "default_branch": "trunk",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 161005,
          "node_id": "MDEwOlJlcG9zaXRvcnkxNjEwMDU=",
          "name": "jspwiki",
          "full_name": "apache/jspwiki",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/jspwiki",
          "description": "Apache JSPWiki is a leading open source WikiWiki engine, feature-rich and built around standard JEE components (Java, servlets, JSP)",
          "fork": false,
          "url": "https://api.github.com/repos/apache/jspwiki",
          "forks_url": "https://api.github.com/repos/apache/jspwiki/forks",
          "keys_url": "https://api.github.com/repos/apache/jspwiki/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/jspwiki/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/jspwiki/teams",
          "hooks_url": "https://api.github.com/repos/apache/jspwiki/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/jspwiki/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/jspwiki/events",
          "assignees_url": "https://api.github.com/repos/apache/jspwiki/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/jspwiki/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/jspwiki/tags",
          "blobs_url": "https://api.github.com/repos/apache/jspwiki/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/jspwiki/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/jspwiki/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/jspwiki/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/jspwiki/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/jspwiki/languages",
          "stargazers_url": "https://api.github.com/repos/apache/jspwiki/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/jspwiki/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/jspwiki/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/jspwiki/subscription",
          "commits_url": "https://api.github.com/repos/apache/jspwiki/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/jspwiki/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/jspwiki/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/jspwiki/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/jspwiki/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/jspwiki/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/jspwiki/merges",
          "archive_url": "https://api.github.com/repos/apache/jspwiki/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/jspwiki/downloads",
          "issues_url": "https://api.github.com/repos/apache/jspwiki/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/jspwiki/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/jspwiki/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/jspwiki/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/jspwiki/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/jspwiki/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/jspwiki/deployments",
          "created_at": "2009-03-27T15:41:58Z",
          "updated_at": "2023-01-10T08:19:48Z",
          "pushed_at": "2023-02-09T04:12:06Z",
          "git_url": "git://github.com/apache/jspwiki.git",
          "ssh_url": "**************:apache/jspwiki.git",
          "clone_url": "https://github.com/apache/jspwiki.git",
          "svn_url": "https://github.com/apache/jspwiki",
          "homepage": "https://jspwiki.apache.org/",
          "size": 92014,
          "stargazers_count": 89,
          "watchers_count": 89,
          "language": "Java",
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 89,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 27,
          "license": {
            "key": "apache-2.0",
            "name": "Apache License 2.0",
            "spdx_id": "Apache-2.0",
            "url": "https://api.github.com/licenses/apache-2.0",
            "node_id": "MDc6TGljZW5zZTI="
          },
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "apache",
            "asf",
            "content",
            "java",
            "jspwiki",
            "wiki"
          ],
          "visibility": "public",
          "forks": 89,
          "open_issues": 27,
          "watchers": 89,
          "default_branch": "master",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 205402,
          "node_id": "MDEwOlJlcG9zaXRvcnkyMDU0MDI=",
          "name": "spamassassin",
          "full_name": "apache/spamassassin",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/spamassassin",
          "description": "Read-only mirror of Apache SpamAssassin. Submit patches to https://bz.apache.org/SpamAssassin/. Do not send pull requests",
          "fork": false,
          "url": "https://api.github.com/repos/apache/spamassassin",
          "forks_url": "https://api.github.com/repos/apache/spamassassin/forks",
          "keys_url": "https://api.github.com/repos/apache/spamassassin/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/spamassassin/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/spamassassin/teams",
          "hooks_url": "https://api.github.com/repos/apache/spamassassin/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/spamassassin/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/spamassassin/events",
          "assignees_url": "https://api.github.com/repos/apache/spamassassin/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/spamassassin/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/spamassassin/tags",
          "blobs_url": "https://api.github.com/repos/apache/spamassassin/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/spamassassin/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/spamassassin/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/spamassassin/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/spamassassin/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/spamassassin/languages",
          "stargazers_url": "https://api.github.com/repos/apache/spamassassin/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/spamassassin/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/spamassassin/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/spamassassin/subscription",
          "commits_url": "https://api.github.com/repos/apache/spamassassin/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/spamassassin/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/spamassassin/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/spamassassin/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/spamassassin/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/spamassassin/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/spamassassin/merges",
          "archive_url": "https://api.github.com/repos/apache/spamassassin/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/spamassassin/downloads",
          "issues_url": "https://api.github.com/repos/apache/spamassassin/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/spamassassin/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/spamassassin/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/spamassassin/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/spamassassin/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/spamassassin/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/spamassassin/deployments",
          "created_at": "2009-05-20T01:47:48Z",
          "updated_at": "2023-02-17T02:40:53Z",
          "pushed_at": "2023-02-17T03:44:24Z",
          "git_url": "git://github.com/apache/spamassassin.git",
          "ssh_url": "**************:apache/spamassassin.git",
          "clone_url": "https://github.com/apache/spamassassin.git",
          "svn_url": "https://github.com/apache/spamassassin",
          "homepage": "http://spamassassin.apache.org",
          "size": 77483,
          "stargazers_count": 233,
          "watchers_count": 233,
          "language": "Perl",
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 62,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 0,
          "license": {
            "key": "apache-2.0",
            "name": "Apache License 2.0",
            "spdx_id": "Apache-2.0",
            "url": "https://api.github.com/licenses/apache-2.0",
            "node_id": "MDc6TGljZW5zZTI="
          },
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "c",
            "mail",
            "perl",
            "spamassassin"
          ],
          "visibility": "public",
          "forks": 62,
          "open_issues": 0,
          "watchers": 233,
          "default_branch": "trunk",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 205403,
          "node_id": "MDEwOlJlcG9zaXRvcnkyMDU0MDM=",
          "name": "ofbiz",
          "full_name": "apache/ofbiz",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/ofbiz",
          "description": "Apache OFBiz - Main development has moved to the ofbiz-frameworks repository.",
          "fork": false,
          "url": "https://api.github.com/repos/apache/ofbiz",
          "forks_url": "https://api.github.com/repos/apache/ofbiz/forks",
          "keys_url": "https://api.github.com/repos/apache/ofbiz/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/ofbiz/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/ofbiz/teams",
          "hooks_url": "https://api.github.com/repos/apache/ofbiz/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/ofbiz/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/ofbiz/events",
          "assignees_url": "https://api.github.com/repos/apache/ofbiz/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/ofbiz/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/ofbiz/tags",
          "blobs_url": "https://api.github.com/repos/apache/ofbiz/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/ofbiz/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/ofbiz/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/ofbiz/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/ofbiz/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/ofbiz/languages",
          "stargazers_url": "https://api.github.com/repos/apache/ofbiz/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/ofbiz/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/ofbiz/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/ofbiz/subscription",
          "commits_url": "https://api.github.com/repos/apache/ofbiz/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/ofbiz/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/ofbiz/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/ofbiz/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/ofbiz/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/ofbiz/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/ofbiz/merges",
          "archive_url": "https://api.github.com/repos/apache/ofbiz/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/ofbiz/downloads",
          "issues_url": "https://api.github.com/repos/apache/ofbiz/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/ofbiz/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/ofbiz/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/ofbiz/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/ofbiz/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/ofbiz/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/ofbiz/deployments",
          "created_at": "2009-05-20T01:47:56Z",
          "updated_at": "2023-02-12T22:11:37Z",
          "pushed_at": "2020-04-27T06:10:43Z",
          "git_url": "git://github.com/apache/ofbiz.git",
          "ssh_url": "**************:apache/ofbiz.git",
          "clone_url": "https://github.com/apache/ofbiz.git",
          "svn_url": "https://github.com/apache/ofbiz",
          "homepage": "https://ofbiz.apache.org",
          "size": 892930,
          "stargazers_count": 750,
          "watchers_count": 750,
          "language": "Java",
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 564,
          "mirror_url": null,
          "archived": true,
          "disabled": false,
          "open_issues_count": 12,
          "license": {
            "key": "apache-2.0",
            "name": "Apache License 2.0",
            "spdx_id": "Apache-2.0",
            "url": "https://api.github.com/licenses/apache-2.0",
            "node_id": "MDc6TGljZW5zZTI="
          },
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "content",
            "database",
            "geospatial",
            "groovy",
            "http",
            "java",
            "javascript",
            "network-server",
            "ofbiz",
            "web-framework",
            "xml"
          ],
          "visibility": "public",
          "forks": 564,
          "open_issues": 12,
          "watchers": 750,
          "default_branch": "trunk",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 205407,
          "node_id": "MDEwOlJlcG9zaXRvcnkyMDU0MDc=",
          "name": "directory-studio",
          "full_name": "apache/directory-studio",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/directory-studio",
          "description": "Apache Directory Studio",
          "fork": false,
          "url": "https://api.github.com/repos/apache/directory-studio",
          "forks_url": "https://api.github.com/repos/apache/directory-studio/forks",
          "keys_url": "https://api.github.com/repos/apache/directory-studio/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/directory-studio/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/directory-studio/teams",
          "hooks_url": "https://api.github.com/repos/apache/directory-studio/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/directory-studio/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/directory-studio/events",
          "assignees_url": "https://api.github.com/repos/apache/directory-studio/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/directory-studio/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/directory-studio/tags",
          "blobs_url": "https://api.github.com/repos/apache/directory-studio/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/directory-studio/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/directory-studio/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/directory-studio/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/directory-studio/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/directory-studio/languages",
          "stargazers_url": "https://api.github.com/repos/apache/directory-studio/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/directory-studio/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/directory-studio/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/directory-studio/subscription",
          "commits_url": "https://api.github.com/repos/apache/directory-studio/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/directory-studio/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/directory-studio/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/directory-studio/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/directory-studio/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/directory-studio/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/directory-studio/merges",
          "archive_url": "https://api.github.com/repos/apache/directory-studio/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/directory-studio/downloads",
          "issues_url": "https://api.github.com/repos/apache/directory-studio/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/directory-studio/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/directory-studio/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/directory-studio/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/directory-studio/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/directory-studio/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/directory-studio/deployments",
          "created_at": "2009-05-20T01:52:19Z",
          "updated_at": "2023-02-10T14:15:56Z",
          "pushed_at": "2022-09-08T12:11:13Z",
          "git_url": "git://github.com/apache/directory-studio.git",
          "ssh_url": "**************:apache/directory-studio.git",
          "clone_url": "https://github.com/apache/directory-studio.git",
          "svn_url": "https://github.com/apache/directory-studio",
          "homepage": "",
          "size": 639365,
          "stargazers_count": 98,
          "watchers_count": 98,
          "language": "Java",
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 44,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 6,
          "license": {
            "key": "apache-2.0",
            "name": "Apache License 2.0",
            "spdx_id": "Apache-2.0",
            "url": "https://api.github.com/licenses/apache-2.0",
            "node_id": "MDc6TGljZW5zZTI="
          },
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "directory",
            "java",
            "network-client",
            "network-server"
          ],
          "visibility": "public",
          "forks": 44,
          "open_issues": 6,
          "watchers": 98,
          "default_branch": "master",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 205414,
          "node_id": "MDEwOlJlcG9zaXRvcnkyMDU0MTQ=",
          "name": "felix",
          "full_name": "apache/felix",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/felix",
          "description": "Mirror of Apache Felix",
          "fork": false,
          "url": "https://api.github.com/repos/apache/felix",
          "forks_url": "https://api.github.com/repos/apache/felix/forks",
          "keys_url": "https://api.github.com/repos/apache/felix/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/felix/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/felix/teams",
          "hooks_url": "https://api.github.com/repos/apache/felix/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/felix/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/felix/events",
          "assignees_url": "https://api.github.com/repos/apache/felix/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/felix/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/felix/tags",
          "blobs_url": "https://api.github.com/repos/apache/felix/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/felix/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/felix/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/felix/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/felix/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/felix/languages",
          "stargazers_url": "https://api.github.com/repos/apache/felix/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/felix/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/felix/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/felix/subscription",
          "commits_url": "https://api.github.com/repos/apache/felix/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/felix/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/felix/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/felix/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/felix/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/felix/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/felix/merges",
          "archive_url": "https://api.github.com/repos/apache/felix/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/felix/downloads",
          "issues_url": "https://api.github.com/repos/apache/felix/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/felix/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/felix/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/felix/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/felix/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/felix/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/felix/deployments",
          "created_at": "2009-05-20T02:00:07Z",
          "updated_at": "2023-01-22T17:14:53Z",
          "pushed_at": "2020-03-04T13:45:46Z",
          "git_url": "git://github.com/apache/felix.git",
          "ssh_url": "**************:apache/felix.git",
          "clone_url": "https://github.com/apache/felix.git",
          "svn_url": "https://github.com/apache/felix",
          "homepage": null,
          "size": 96620,
          "stargazers_count": 281,
          "watchers_count": 281,
          "language": null,
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 327,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 59,
          "license": null,
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "felix",
            "java",
            "network-server"
          ],
          "visibility": "public",
          "forks": 327,
          "open_issues": 59,
          "watchers": 281,
          "default_branch": "trunk",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 205415,
          "node_id": "MDEwOlJlcG9zaXRvcnkyMDU0MTU=",
          "name": "chainsaw",
          "full_name": "apache/chainsaw",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/chainsaw",
          "description": "Mirror of Apache Chainsaw",
          "fork": false,
          "url": "https://api.github.com/repos/apache/chainsaw",
          "forks_url": "https://api.github.com/repos/apache/chainsaw/forks",
          "keys_url": "https://api.github.com/repos/apache/chainsaw/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/chainsaw/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/chainsaw/teams",
          "hooks_url": "https://api.github.com/repos/apache/chainsaw/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/chainsaw/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/chainsaw/events",
          "assignees_url": "https://api.github.com/repos/apache/chainsaw/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/chainsaw/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/chainsaw/tags",
          "blobs_url": "https://api.github.com/repos/apache/chainsaw/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/chainsaw/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/chainsaw/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/chainsaw/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/chainsaw/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/chainsaw/languages",
          "stargazers_url": "https://api.github.com/repos/apache/chainsaw/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/chainsaw/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/chainsaw/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/chainsaw/subscription",
          "commits_url": "https://api.github.com/repos/apache/chainsaw/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/chainsaw/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/chainsaw/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/chainsaw/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/chainsaw/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/chainsaw/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/chainsaw/merges",
          "archive_url": "https://api.github.com/repos/apache/chainsaw/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/chainsaw/downloads",
          "issues_url": "https://api.github.com/repos/apache/chainsaw/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/chainsaw/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/chainsaw/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/chainsaw/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/chainsaw/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/chainsaw/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/chainsaw/deployments",
          "created_at": "2009-05-20T02:00:33Z",
          "updated_at": "2022-11-26T12:18:02Z",
          "pushed_at": "2022-07-08T18:14:36Z",
          "git_url": "git://github.com/apache/chainsaw.git",
          "ssh_url": "**************:apache/chainsaw.git",
          "clone_url": "https://github.com/apache/chainsaw.git",
          "svn_url": "https://github.com/apache/chainsaw",
          "homepage": null,
          "size": 2704,
          "stargazers_count": 18,
          "watchers_count": 18,
          "language": "Java",
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 20,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 2,
          "license": {
            "key": "apache-2.0",
            "name": "Apache License 2.0",
            "spdx_id": "Apache-2.0",
            "url": "https://api.github.com/licenses/apache-2.0",
            "node_id": "MDc6TGljZW5zZTI="
          },
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "chainsaw"
          ],
          "visibility": "public",
          "forks": 20,
          "open_issues": 2,
          "watchers": 18,
          "default_branch": "trunk",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 205417,
          "node_id": "MDEwOlJlcG9zaXRvcnkyMDU0MTc=",
          "name": "maven-wagon",
          "full_name": "apache/maven-wagon",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/maven-wagon",
          "description": "Apache Maven Wagon",
          "fork": false,
          "url": "https://api.github.com/repos/apache/maven-wagon",
          "forks_url": "https://api.github.com/repos/apache/maven-wagon/forks",
          "keys_url": "https://api.github.com/repos/apache/maven-wagon/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/maven-wagon/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/maven-wagon/teams",
          "hooks_url": "https://api.github.com/repos/apache/maven-wagon/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/maven-wagon/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/maven-wagon/events",
          "assignees_url": "https://api.github.com/repos/apache/maven-wagon/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/maven-wagon/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/maven-wagon/tags",
          "blobs_url": "https://api.github.com/repos/apache/maven-wagon/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/maven-wagon/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/maven-wagon/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/maven-wagon/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/maven-wagon/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/maven-wagon/languages",
          "stargazers_url": "https://api.github.com/repos/apache/maven-wagon/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/maven-wagon/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/maven-wagon/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/maven-wagon/subscription",
          "commits_url": "https://api.github.com/repos/apache/maven-wagon/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/maven-wagon/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/maven-wagon/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/maven-wagon/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/maven-wagon/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/maven-wagon/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/maven-wagon/merges",
          "archive_url": "https://api.github.com/repos/apache/maven-wagon/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/maven-wagon/downloads",
          "issues_url": "https://api.github.com/repos/apache/maven-wagon/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/maven-wagon/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/maven-wagon/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/maven-wagon/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/maven-wagon/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/maven-wagon/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/maven-wagon/deployments",
          "created_at": "2009-05-20T02:01:12Z",
          "updated_at": "2022-11-04T14:46:35Z",
          "pushed_at": "2022-12-18T21:06:50Z",
          "git_url": "git://github.com/apache/maven-wagon.git",
          "ssh_url": "**************:apache/maven-wagon.git",
          "clone_url": "https://github.com/apache/maven-wagon.git",
          "svn_url": "https://github.com/apache/maven-wagon",
          "homepage": "https://maven.apache.org/wagon/",
          "size": 4922,
          "stargazers_count": 43,
          "watchers_count": 43,
          "language": "Java",
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 101,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 5,
          "license": null,
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "build-management",
            "java",
            "maven",
            "maven-plugins",
            "maven-wagon"
          ],
          "visibility": "public",
          "forks": 101,
          "open_issues": 5,
          "watchers": 43,
          "default_branch": "master",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 205418,
          "node_id": "MDEwOlJlcG9zaXRvcnkyMDU0MTg=",
          "name": "maven-resources",
          "full_name": "apache/maven-resources",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/maven-resources",
          "description": "[deprecated] Mirror of Apache Maven resources",
          "fork": false,
          "url": "https://api.github.com/repos/apache/maven-resources",
          "forks_url": "https://api.github.com/repos/apache/maven-resources/forks",
          "keys_url": "https://api.github.com/repos/apache/maven-resources/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/maven-resources/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/maven-resources/teams",
          "hooks_url": "https://api.github.com/repos/apache/maven-resources/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/maven-resources/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/maven-resources/events",
          "assignees_url": "https://api.github.com/repos/apache/maven-resources/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/maven-resources/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/maven-resources/tags",
          "blobs_url": "https://api.github.com/repos/apache/maven-resources/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/maven-resources/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/maven-resources/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/maven-resources/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/maven-resources/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/maven-resources/languages",
          "stargazers_url": "https://api.github.com/repos/apache/maven-resources/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/maven-resources/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/maven-resources/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/maven-resources/subscription",
          "commits_url": "https://api.github.com/repos/apache/maven-resources/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/maven-resources/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/maven-resources/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/maven-resources/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/maven-resources/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/maven-resources/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/maven-resources/merges",
          "archive_url": "https://api.github.com/repos/apache/maven-resources/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/maven-resources/downloads",
          "issues_url": "https://api.github.com/repos/apache/maven-resources/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/maven-resources/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/maven-resources/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/maven-resources/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/maven-resources/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/maven-resources/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/maven-resources/deployments",
          "created_at": "2009-05-20T02:01:27Z",
          "updated_at": "2023-01-28T15:58:18Z",
          "pushed_at": "2019-02-19T01:29:02Z",
          "git_url": "git://github.com/apache/maven-resources.git",
          "ssh_url": "**************:apache/maven-resources.git",
          "clone_url": "https://github.com/apache/maven-resources.git",
          "svn_url": "https://github.com/apache/maven-resources",
          "homepage": null,
          "size": 188,
          "stargazers_count": 3,
          "watchers_count": 3,
          "language": "Java",
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 11,
          "mirror_url": null,
          "archived": true,
          "disabled": false,
          "open_issues_count": 1,
          "license": null,
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "build-management",
            "java",
            "maven"
          ],
          "visibility": "public",
          "forks": 11,
          "open_issues": 1,
          "watchers": 3,
          "default_branch": "trunk",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 205420,
          "node_id": "MDEwOlJlcG9zaXRvcnkyMDU0MjA=",
          "name": "harmony-drlvm",
          "full_name": "apache/harmony-drlvm",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/harmony-drlvm",
          "description": "Mirror of Apache Harmony DRLVM",
          "fork": false,
          "url": "https://api.github.com/repos/apache/harmony-drlvm",
          "forks_url": "https://api.github.com/repos/apache/harmony-drlvm/forks",
          "keys_url": "https://api.github.com/repos/apache/harmony-drlvm/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/harmony-drlvm/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/harmony-drlvm/teams",
          "hooks_url": "https://api.github.com/repos/apache/harmony-drlvm/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/harmony-drlvm/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/harmony-drlvm/events",
          "assignees_url": "https://api.github.com/repos/apache/harmony-drlvm/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/harmony-drlvm/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/harmony-drlvm/tags",
          "blobs_url": "https://api.github.com/repos/apache/harmony-drlvm/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/harmony-drlvm/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/harmony-drlvm/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/harmony-drlvm/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/harmony-drlvm/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/harmony-drlvm/languages",
          "stargazers_url": "https://api.github.com/repos/apache/harmony-drlvm/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/harmony-drlvm/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/harmony-drlvm/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/harmony-drlvm/subscription",
          "commits_url": "https://api.github.com/repos/apache/harmony-drlvm/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/harmony-drlvm/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/harmony-drlvm/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/harmony-drlvm/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/harmony-drlvm/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/harmony-drlvm/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/harmony-drlvm/merges",
          "archive_url": "https://api.github.com/repos/apache/harmony-drlvm/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/harmony-drlvm/downloads",
          "issues_url": "https://api.github.com/repos/apache/harmony-drlvm/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/harmony-drlvm/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/harmony-drlvm/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/harmony-drlvm/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/harmony-drlvm/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/harmony-drlvm/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/harmony-drlvm/deployments",
          "created_at": "2009-05-20T02:01:55Z",
          "updated_at": "2022-06-28T03:17:29Z",
          "pushed_at": "2010-03-21T06:40:13Z",
          "git_url": "git://github.com/apache/harmony-drlvm.git",
          "ssh_url": "**************:apache/harmony-drlvm.git",
          "clone_url": "https://github.com/apache/harmony-drlvm.git",
          "svn_url": "https://github.com/apache/harmony-drlvm",
          "homepage": null,
          "size": 12420,
          "stargazers_count": 12,
          "watchers_count": 12,
          "language": "C++",
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 10,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 0,
          "license": {
            "key": "apache-2.0",
            "name": "Apache License 2.0",
            "spdx_id": "Apache-2.0",
            "url": "https://api.github.com/licenses/apache-2.0",
            "node_id": "MDc6TGljZW5zZTI="
          },
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "harmony"
          ],
          "visibility": "public",
          "forks": 10,
          "open_issues": 0,
          "watchers": 12,
          "default_branch": "trunk",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 205422,
          "node_id": "MDEwOlJlcG9zaXRvcnkyMDU0MjI=",
          "name": "struts-maven",
          "full_name": "apache/struts-maven",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/struts-maven",
          "description": "Mirror of Apache Struts Maven",
          "fork": false,
          "url": "https://api.github.com/repos/apache/struts-maven",
          "forks_url": "https://api.github.com/repos/apache/struts-maven/forks",
          "keys_url": "https://api.github.com/repos/apache/struts-maven/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/struts-maven/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/struts-maven/teams",
          "hooks_url": "https://api.github.com/repos/apache/struts-maven/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/struts-maven/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/struts-maven/events",
          "assignees_url": "https://api.github.com/repos/apache/struts-maven/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/struts-maven/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/struts-maven/tags",
          "blobs_url": "https://api.github.com/repos/apache/struts-maven/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/struts-maven/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/struts-maven/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/struts-maven/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/struts-maven/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/struts-maven/languages",
          "stargazers_url": "https://api.github.com/repos/apache/struts-maven/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/struts-maven/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/struts-maven/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/struts-maven/subscription",
          "commits_url": "https://api.github.com/repos/apache/struts-maven/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/struts-maven/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/struts-maven/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/struts-maven/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/struts-maven/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/struts-maven/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/struts-maven/merges",
          "archive_url": "https://api.github.com/repos/apache/struts-maven/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/struts-maven/downloads",
          "issues_url": "https://api.github.com/repos/apache/struts-maven/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/struts-maven/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/struts-maven/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/struts-maven/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/struts-maven/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/struts-maven/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/struts-maven/deployments",
          "created_at": "2009-05-20T02:02:29Z",
          "updated_at": "2021-11-10T13:22:09Z",
          "pushed_at": "2017-04-28T16:01:34Z",
          "git_url": "git://github.com/apache/struts-maven.git",
          "ssh_url": "**************:apache/struts-maven.git",
          "clone_url": "https://github.com/apache/struts-maven.git",
          "svn_url": "https://github.com/apache/struts-maven",
          "homepage": null,
          "size": 862,
          "stargazers_count": 3,
          "watchers_count": 3,
          "language": null,
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 5,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 0,
          "license": null,
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "java",
            "struts",
            "web-framework"
          ],
          "visibility": "public",
          "forks": 5,
          "open_issues": 0,
          "watchers": 3,
          "default_branch": "trunk",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 205423,
          "node_id": "MDEwOlJlcG9zaXRvcnkyMDU0MjM=",
          "name": "httpd",
          "full_name": "apache/httpd",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/httpd",
          "description": "Mirror of Apache HTTP Server. Issues: http://issues.apache.org",
          "fork": false,
          "url": "https://api.github.com/repos/apache/httpd",
          "forks_url": "https://api.github.com/repos/apache/httpd/forks",
          "keys_url": "https://api.github.com/repos/apache/httpd/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/httpd/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/httpd/teams",
          "hooks_url": "https://api.github.com/repos/apache/httpd/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/httpd/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/httpd/events",
          "assignees_url": "https://api.github.com/repos/apache/httpd/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/httpd/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/httpd/tags",
          "blobs_url": "https://api.github.com/repos/apache/httpd/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/httpd/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/httpd/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/httpd/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/httpd/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/httpd/languages",
          "stargazers_url": "https://api.github.com/repos/apache/httpd/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/httpd/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/httpd/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/httpd/subscription",
          "commits_url": "https://api.github.com/repos/apache/httpd/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/httpd/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/httpd/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/httpd/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/httpd/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/httpd/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/httpd/merges",
          "archive_url": "https://api.github.com/repos/apache/httpd/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/httpd/downloads",
          "issues_url": "https://api.github.com/repos/apache/httpd/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/httpd/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/httpd/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/httpd/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/httpd/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/httpd/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/httpd/deployments",
          "created_at": "2009-05-20T02:02:59Z",
          "updated_at": "2023-02-17T06:14:48Z",
          "pushed_at": "2023-02-16T19:25:32Z",
          "git_url": "git://github.com/apache/httpd.git",
          "ssh_url": "**************:apache/httpd.git",
          "clone_url": "https://github.com/apache/httpd.git",
          "svn_url": "https://github.com/apache/httpd",
          "homepage": "https://httpd.apache.org",
          "size": 318135,
          "stargazers_count": 3148,
          "watchers_count": 3148,
          "language": "C",
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 1033,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 56,
          "license": {
            "key": "apache-2.0",
            "name": "Apache License 2.0",
            "spdx_id": "Apache-2.0",
            "url": "https://api.github.com/licenses/apache-2.0",
            "node_id": "MDc6TGljZW5zZTI="
          },
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "httpd"
          ],
          "visibility": "public",
          "forks": 1033,
          "open_issues": 56,
          "watchers": 3148,
          "default_branch": "trunk",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 206317,
          "node_id": "MDEwOlJlcG9zaXRvcnkyMDYzMTc=",
          "name": "camel",
          "full_name": "apache/camel",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/camel",
          "description": "Apache Camel is an open source integration framework that empowers you to quickly and easily integrate various systems consuming or producing data.",
          "fork": false,
          "url": "https://api.github.com/repos/apache/camel",
          "forks_url": "https://api.github.com/repos/apache/camel/forks",
          "keys_url": "https://api.github.com/repos/apache/camel/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/camel/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/camel/teams",
          "hooks_url": "https://api.github.com/repos/apache/camel/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/camel/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/camel/events",
          "assignees_url": "https://api.github.com/repos/apache/camel/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/camel/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/camel/tags",
          "blobs_url": "https://api.github.com/repos/apache/camel/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/camel/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/camel/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/camel/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/camel/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/camel/languages",
          "stargazers_url": "https://api.github.com/repos/apache/camel/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/camel/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/camel/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/camel/subscription",
          "commits_url": "https://api.github.com/repos/apache/camel/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/camel/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/camel/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/camel/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/camel/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/camel/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/camel/merges",
          "archive_url": "https://api.github.com/repos/apache/camel/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/camel/downloads",
          "issues_url": "https://api.github.com/repos/apache/camel/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/camel/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/camel/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/camel/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/camel/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/camel/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/camel/deployments",
          "created_at": "2009-05-21T00:25:36Z",
          "updated_at": "2023-02-16T19:20:12Z",
          "pushed_at": "2023-02-17T04:51:36Z",
          "git_url": "git://github.com/apache/camel.git",
          "ssh_url": "**************:apache/camel.git",
          "clone_url": "https://github.com/apache/camel.git",
          "svn_url": "https://github.com/apache/camel",
          "homepage": "https://camel.apache.org",
          "size": 750516,
          "stargazers_count": 4726,
          "watchers_count": 4726,
          "language": "Java",
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 4679,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 12,
          "license": {
            "key": "apache-2.0",
            "name": "Apache License 2.0",
            "spdx_id": "Apache-2.0",
            "url": "https://api.github.com/licenses/apache-2.0",
            "node_id": "MDc6TGljZW5zZTI="
          },
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "camel",
            "integration",
            "java"
          ],
          "visibility": "public",
          "forks": 4679,
          "open_issues": 12,
          "watchers": 4726,
          "default_branch": "main",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 206318,
          "node_id": "MDEwOlJlcG9zaXRvcnkyMDYzMTg=",
          "name": "xmlgraphics-fop",
          "full_name": "apache/xmlgraphics-fop",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/xmlgraphics-fop",
          "description": "Mirror of Apache FOP",
          "fork": false,
          "url": "https://api.github.com/repos/apache/xmlgraphics-fop",
          "forks_url": "https://api.github.com/repos/apache/xmlgraphics-fop/forks",
          "keys_url": "https://api.github.com/repos/apache/xmlgraphics-fop/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/xmlgraphics-fop/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/xmlgraphics-fop/teams",
          "hooks_url": "https://api.github.com/repos/apache/xmlgraphics-fop/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/xmlgraphics-fop/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/xmlgraphics-fop/events",
          "assignees_url": "https://api.github.com/repos/apache/xmlgraphics-fop/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/xmlgraphics-fop/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/xmlgraphics-fop/tags",
          "blobs_url": "https://api.github.com/repos/apache/xmlgraphics-fop/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/xmlgraphics-fop/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/xmlgraphics-fop/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/xmlgraphics-fop/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/xmlgraphics-fop/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/xmlgraphics-fop/languages",
          "stargazers_url": "https://api.github.com/repos/apache/xmlgraphics-fop/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/xmlgraphics-fop/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/xmlgraphics-fop/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/xmlgraphics-fop/subscription",
          "commits_url": "https://api.github.com/repos/apache/xmlgraphics-fop/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/xmlgraphics-fop/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/xmlgraphics-fop/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/xmlgraphics-fop/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/xmlgraphics-fop/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/xmlgraphics-fop/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/xmlgraphics-fop/merges",
          "archive_url": "https://api.github.com/repos/apache/xmlgraphics-fop/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/xmlgraphics-fop/downloads",
          "issues_url": "https://api.github.com/repos/apache/xmlgraphics-fop/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/xmlgraphics-fop/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/xmlgraphics-fop/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/xmlgraphics-fop/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/xmlgraphics-fop/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/xmlgraphics-fop/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/xmlgraphics-fop/deployments",
          "created_at": "2009-05-21T00:26:43Z",
          "updated_at": "2023-01-02T19:34:11Z",
          "pushed_at": "2023-02-03T08:49:24Z",
          "git_url": "git://github.com/apache/xmlgraphics-fop.git",
          "ssh_url": "**************:apache/xmlgraphics-fop.git",
          "clone_url": "https://github.com/apache/xmlgraphics-fop.git",
          "svn_url": "https://github.com/apache/xmlgraphics-fop",
          "homepage": null,
          "size": 214208,
          "stargazers_count": 149,
          "watchers_count": 149,
          "language": "Java",
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 118,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 11,
          "license": {
            "key": "apache-2.0",
            "name": "Apache License 2.0",
            "spdx_id": "Apache-2.0",
            "url": "https://api.github.com/licenses/apache-2.0",
            "node_id": "MDc6TGljZW5zZTI="
          },
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "fop"
          ],
          "visibility": "public",
          "forks": 118,
          "open_issues": 11,
          "watchers": 149,
          "default_branch": "trunk",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 206320,
          "node_id": "MDEwOlJlcG9zaXRvcnkyMDYzMjA=",
          "name": "maven-scm",
          "full_name": "apache/maven-scm",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/maven-scm",
          "description": "Apache Maven SCM (Plugin)",
          "fork": false,
          "url": "https://api.github.com/repos/apache/maven-scm",
          "forks_url": "https://api.github.com/repos/apache/maven-scm/forks",
          "keys_url": "https://api.github.com/repos/apache/maven-scm/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/maven-scm/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/maven-scm/teams",
          "hooks_url": "https://api.github.com/repos/apache/maven-scm/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/maven-scm/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/maven-scm/events",
          "assignees_url": "https://api.github.com/repos/apache/maven-scm/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/maven-scm/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/maven-scm/tags",
          "blobs_url": "https://api.github.com/repos/apache/maven-scm/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/maven-scm/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/maven-scm/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/maven-scm/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/maven-scm/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/maven-scm/languages",
          "stargazers_url": "https://api.github.com/repos/apache/maven-scm/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/maven-scm/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/maven-scm/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/maven-scm/subscription",
          "commits_url": "https://api.github.com/repos/apache/maven-scm/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/maven-scm/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/maven-scm/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/maven-scm/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/maven-scm/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/maven-scm/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/maven-scm/merges",
          "archive_url": "https://api.github.com/repos/apache/maven-scm/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/maven-scm/downloads",
          "issues_url": "https://api.github.com/repos/apache/maven-scm/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/maven-scm/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/maven-scm/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/maven-scm/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/maven-scm/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/maven-scm/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/maven-scm/deployments",
          "created_at": "2009-05-21T00:33:04Z",
          "updated_at": "2023-02-12T01:16:59Z",
          "pushed_at": "2022-11-02T17:42:36Z",
          "git_url": "git://github.com/apache/maven-scm.git",
          "ssh_url": "**************:apache/maven-scm.git",
          "clone_url": "https://github.com/apache/maven-scm.git",
          "svn_url": "https://github.com/apache/maven-scm",
          "homepage": "https://maven.apache.org/scm/",
          "size": 12803,
          "stargazers_count": 84,
          "watchers_count": 84,
          "language": "Java",
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 169,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 2,
          "license": null,
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "build-management",
            "java",
            "maven",
            "maven-plugins",
            "maven-scm-plugin"
          ],
          "visibility": "public",
          "forks": 169,
          "open_issues": 2,
          "watchers": 84,
          "default_branch": "master",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 206322,
          "node_id": "MDEwOlJlcG9zaXRvcnkyMDYzMjI=",
          "name": "maven-plugins",
          "full_name": "apache/maven-plugins",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/maven-plugins",
          "description": "[deprecated] Mirror of Apache Maven plugins",
          "fork": false,
          "url": "https://api.github.com/repos/apache/maven-plugins",
          "forks_url": "https://api.github.com/repos/apache/maven-plugins/forks",
          "keys_url": "https://api.github.com/repos/apache/maven-plugins/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/maven-plugins/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/maven-plugins/teams",
          "hooks_url": "https://api.github.com/repos/apache/maven-plugins/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/maven-plugins/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/maven-plugins/events",
          "assignees_url": "https://api.github.com/repos/apache/maven-plugins/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/maven-plugins/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/maven-plugins/tags",
          "blobs_url": "https://api.github.com/repos/apache/maven-plugins/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/maven-plugins/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/maven-plugins/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/maven-plugins/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/maven-plugins/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/maven-plugins/languages",
          "stargazers_url": "https://api.github.com/repos/apache/maven-plugins/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/maven-plugins/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/maven-plugins/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/maven-plugins/subscription",
          "commits_url": "https://api.github.com/repos/apache/maven-plugins/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/maven-plugins/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/maven-plugins/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/maven-plugins/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/maven-plugins/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/maven-plugins/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/maven-plugins/merges",
          "archive_url": "https://api.github.com/repos/apache/maven-plugins/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/maven-plugins/downloads",
          "issues_url": "https://api.github.com/repos/apache/maven-plugins/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/maven-plugins/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/maven-plugins/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/maven-plugins/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/maven-plugins/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/maven-plugins/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/maven-plugins/deployments",
          "created_at": "2009-05-21T00:33:19Z",
          "updated_at": "2023-02-13T17:31:40Z",
          "pushed_at": "2019-11-13T03:16:16Z",
          "git_url": "git://github.com/apache/maven-plugins.git",
          "ssh_url": "**************:apache/maven-plugins.git",
          "clone_url": "https://github.com/apache/maven-plugins.git",
          "svn_url": "https://github.com/apache/maven-plugins",
          "homepage": null,
          "size": 55676,
          "stargazers_count": 241,
          "watchers_count": 241,
          "language": "Java",
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 283,
          "mirror_url": null,
          "archived": true,
          "disabled": false,
          "open_issues_count": 17,
          "license": {
            "key": "apache-2.0",
            "name": "Apache License 2.0",
            "spdx_id": "Apache-2.0",
            "url": "https://api.github.com/licenses/apache-2.0",
            "node_id": "MDc6TGljZW5zZTI="
          },
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "build-management",
            "java",
            "maven"
          ],
          "visibility": "public",
          "forks": 283,
          "open_issues": 17,
          "watchers": 241,
          "default_branch": "trunk",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 206335,
          "node_id": "MDEwOlJlcG9zaXRvcnkyMDYzMzU=",
          "name": "directory-samples",
          "full_name": "apache/directory-samples",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/directory-samples",
          "description": "Apache Directory Samples",
          "fork": false,
          "url": "https://api.github.com/repos/apache/directory-samples",
          "forks_url": "https://api.github.com/repos/apache/directory-samples/forks",
          "keys_url": "https://api.github.com/repos/apache/directory-samples/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/directory-samples/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/directory-samples/teams",
          "hooks_url": "https://api.github.com/repos/apache/directory-samples/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/directory-samples/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/directory-samples/events",
          "assignees_url": "https://api.github.com/repos/apache/directory-samples/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/directory-samples/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/directory-samples/tags",
          "blobs_url": "https://api.github.com/repos/apache/directory-samples/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/directory-samples/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/directory-samples/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/directory-samples/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/directory-samples/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/directory-samples/languages",
          "stargazers_url": "https://api.github.com/repos/apache/directory-samples/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/directory-samples/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/directory-samples/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/directory-samples/subscription",
          "commits_url": "https://api.github.com/repos/apache/directory-samples/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/directory-samples/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/directory-samples/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/directory-samples/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/directory-samples/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/directory-samples/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/directory-samples/merges",
          "archive_url": "https://api.github.com/repos/apache/directory-samples/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/directory-samples/downloads",
          "issues_url": "https://api.github.com/repos/apache/directory-samples/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/directory-samples/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/directory-samples/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/directory-samples/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/directory-samples/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/directory-samples/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/directory-samples/deployments",
          "created_at": "2009-05-21T00:57:36Z",
          "updated_at": "2021-11-10T15:33:09Z",
          "pushed_at": "2017-11-23T03:57:50Z",
          "git_url": "git://github.com/apache/directory-samples.git",
          "ssh_url": "**************:apache/directory-samples.git",
          "clone_url": "https://github.com/apache/directory-samples.git",
          "svn_url": "https://github.com/apache/directory-samples",
          "homepage": "",
          "size": 47,
          "stargazers_count": 4,
          "watchers_count": 4,
          "language": "Java",
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 6,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 0,
          "license": null,
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "directory",
            "java",
            "network-client",
            "network-server"
          ],
          "visibility": "public",
          "forks": 6,
          "open_issues": 0,
          "watchers": 4,
          "default_branch": "master",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 206339,
          "node_id": "MDEwOlJlcG9zaXRvcnkyMDYzMzk=",
          "name": "maven-release",
          "full_name": "apache/maven-release",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/maven-release",
          "description": "Apache Maven Release (Plugin)",
          "fork": false,
          "url": "https://api.github.com/repos/apache/maven-release",
          "forks_url": "https://api.github.com/repos/apache/maven-release/forks",
          "keys_url": "https://api.github.com/repos/apache/maven-release/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/maven-release/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/maven-release/teams",
          "hooks_url": "https://api.github.com/repos/apache/maven-release/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/maven-release/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/maven-release/events",
          "assignees_url": "https://api.github.com/repos/apache/maven-release/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/maven-release/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/maven-release/tags",
          "blobs_url": "https://api.github.com/repos/apache/maven-release/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/maven-release/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/maven-release/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/maven-release/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/maven-release/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/maven-release/languages",
          "stargazers_url": "https://api.github.com/repos/apache/maven-release/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/maven-release/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/maven-release/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/maven-release/subscription",
          "commits_url": "https://api.github.com/repos/apache/maven-release/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/maven-release/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/maven-release/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/maven-release/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/maven-release/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/maven-release/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/maven-release/merges",
          "archive_url": "https://api.github.com/repos/apache/maven-release/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/maven-release/downloads",
          "issues_url": "https://api.github.com/repos/apache/maven-release/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/maven-release/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/maven-release/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/maven-release/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/maven-release/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/maven-release/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/maven-release/deployments",
          "created_at": "2009-05-21T00:59:16Z",
          "updated_at": "2023-02-13T17:00:14Z",
          "pushed_at": "2023-02-06T15:49:14Z",
          "git_url": "git://github.com/apache/maven-release.git",
          "ssh_url": "**************:apache/maven-release.git",
          "clone_url": "https://github.com/apache/maven-release.git",
          "svn_url": "https://github.com/apache/maven-release",
          "homepage": "https://maven.apache.org/maven-release/",
          "size": 3658,
          "stargazers_count": 95,
          "watchers_count": 95,
          "language": "Java",
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 127,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 13,
          "license": null,
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "build-management",
            "java",
            "maven",
            "maven-plugins",
            "maven-release-plugin"
          ],
          "visibility": "public",
          "forks": 127,
          "open_issues": 13,
          "watchers": 95,
          "default_branch": "master",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 206341,
          "node_id": "MDEwOlJlcG9zaXRvcnkyMDYzNDE=",
          "name": "maven-enforcer",
          "full_name": "apache/maven-enforcer",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/maven-enforcer",
          "description": "Apache Maven Enforcer (Plugin)",
          "fork": false,
          "url": "https://api.github.com/repos/apache/maven-enforcer",
          "forks_url": "https://api.github.com/repos/apache/maven-enforcer/forks",
          "keys_url": "https://api.github.com/repos/apache/maven-enforcer/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/maven-enforcer/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/maven-enforcer/teams",
          "hooks_url": "https://api.github.com/repos/apache/maven-enforcer/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/maven-enforcer/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/maven-enforcer/events",
          "assignees_url": "https://api.github.com/repos/apache/maven-enforcer/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/maven-enforcer/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/maven-enforcer/tags",
          "blobs_url": "https://api.github.com/repos/apache/maven-enforcer/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/maven-enforcer/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/maven-enforcer/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/maven-enforcer/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/maven-enforcer/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/maven-enforcer/languages",
          "stargazers_url": "https://api.github.com/repos/apache/maven-enforcer/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/maven-enforcer/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/maven-enforcer/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/maven-enforcer/subscription",
          "commits_url": "https://api.github.com/repos/apache/maven-enforcer/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/maven-enforcer/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/maven-enforcer/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/maven-enforcer/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/maven-enforcer/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/maven-enforcer/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/maven-enforcer/merges",
          "archive_url": "https://api.github.com/repos/apache/maven-enforcer/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/maven-enforcer/downloads",
          "issues_url": "https://api.github.com/repos/apache/maven-enforcer/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/maven-enforcer/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/maven-enforcer/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/maven-enforcer/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/maven-enforcer/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/maven-enforcer/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/maven-enforcer/deployments",
          "created_at": "2009-05-21T00:59:40Z",
          "updated_at": "2023-02-07T15:01:19Z",
          "pushed_at": "2023-02-15T15:58:23Z",
          "git_url": "git://github.com/apache/maven-enforcer.git",
          "ssh_url": "**************:apache/maven-enforcer.git",
          "clone_url": "https://github.com/apache/maven-enforcer.git",
          "svn_url": "https://github.com/apache/maven-enforcer",
          "homepage": "https://maven.apache.org/enforcer/",
          "size": 2395,
          "stargazers_count": 117,
          "watchers_count": 117,
          "language": "Java",
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 141,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 8,
          "license": {
            "key": "apache-2.0",
            "name": "Apache License 2.0",
            "spdx_id": "Apache-2.0",
            "url": "https://api.github.com/licenses/apache-2.0",
            "node_id": "MDc6TGljZW5zZTI="
          },
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "build-management",
            "java",
            "maven",
            "maven-enforcer-plugin",
            "maven-plugins"
          ],
          "visibility": "public",
          "forks": 141,
          "open_issues": 8,
          "watchers": 117,
          "default_branch": "master",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        },
        {
          "id": 206346,
          "node_id": "MDEwOlJlcG9zaXRvcnkyMDYzNDY=",
          "name": "synapse",
          "full_name": "apache/synapse",
          "private": false,
          "owner": {
            "login": "apache",
            "id": 47359,
            "node_id": "MDEyOk9yZ2FuaXphdGlvbjQ3MzU5",
            "avatar_url": "https://avatars.githubusercontent.com/u/47359?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/apache",
            "html_url": "https://github.com/apache",
            "followers_url": "https://api.github.com/users/apache/followers",
            "following_url": "https://api.github.com/users/apache/following{/other_user}",
            "gists_url": "https://api.github.com/users/apache/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/apache/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/apache/subscriptions",
            "organizations_url": "https://api.github.com/users/apache/orgs",
            "repos_url": "https://api.github.com/users/apache/repos",
            "events_url": "https://api.github.com/users/apache/events{/privacy}",
            "received_events_url": "https://api.github.com/users/apache/received_events",
            "type": "Organization",
            "site_admin": false
          },
          "html_url": "https://github.com/apache/synapse",
          "description": "Apache Synapse is a lightweight and high-performance Enterprise Service Bus (ESB)",
          "fork": false,
          "url": "https://api.github.com/repos/apache/synapse",
          "forks_url": "https://api.github.com/repos/apache/synapse/forks",
          "keys_url": "https://api.github.com/repos/apache/synapse/keys{/key_id}",
          "collaborators_url": "https://api.github.com/repos/apache/synapse/collaborators{/collaborator}",
          "teams_url": "https://api.github.com/repos/apache/synapse/teams",
          "hooks_url": "https://api.github.com/repos/apache/synapse/hooks",
          "issue_events_url": "https://api.github.com/repos/apache/synapse/issues/events{/number}",
          "events_url": "https://api.github.com/repos/apache/synapse/events",
          "assignees_url": "https://api.github.com/repos/apache/synapse/assignees{/user}",
          "branches_url": "https://api.github.com/repos/apache/synapse/branches{/branch}",
          "tags_url": "https://api.github.com/repos/apache/synapse/tags",
          "blobs_url": "https://api.github.com/repos/apache/synapse/git/blobs{/sha}",
          "git_tags_url": "https://api.github.com/repos/apache/synapse/git/tags{/sha}",
          "git_refs_url": "https://api.github.com/repos/apache/synapse/git/refs{/sha}",
          "trees_url": "https://api.github.com/repos/apache/synapse/git/trees{/sha}",
          "statuses_url": "https://api.github.com/repos/apache/synapse/statuses/{sha}",
          "languages_url": "https://api.github.com/repos/apache/synapse/languages",
          "stargazers_url": "https://api.github.com/repos/apache/synapse/stargazers",
          "contributors_url": "https://api.github.com/repos/apache/synapse/contributors",
          "subscribers_url": "https://api.github.com/repos/apache/synapse/subscribers",
          "subscription_url": "https://api.github.com/repos/apache/synapse/subscription",
          "commits_url": "https://api.github.com/repos/apache/synapse/commits{/sha}",
          "git_commits_url": "https://api.github.com/repos/apache/synapse/git/commits{/sha}",
          "comments_url": "https://api.github.com/repos/apache/synapse/comments{/number}",
          "issue_comment_url": "https://api.github.com/repos/apache/synapse/issues/comments{/number}",
          "contents_url": "https://api.github.com/repos/apache/synapse/contents/{+path}",
          "compare_url": "https://api.github.com/repos/apache/synapse/compare/{base}...{head}",
          "merges_url": "https://api.github.com/repos/apache/synapse/merges",
          "archive_url": "https://api.github.com/repos/apache/synapse/{archive_format}{/ref}",
          "downloads_url": "https://api.github.com/repos/apache/synapse/downloads",
          "issues_url": "https://api.github.com/repos/apache/synapse/issues{/number}",
          "pulls_url": "https://api.github.com/repos/apache/synapse/pulls{/number}",
          "milestones_url": "https://api.github.com/repos/apache/synapse/milestones{/number}",
          "notifications_url": "https://api.github.com/repos/apache/synapse/notifications{?since,all,participating}",
          "labels_url": "https://api.github.com/repos/apache/synapse/labels{/name}",
          "releases_url": "https://api.github.com/repos/apache/synapse/releases{/id}",
          "deployments_url": "https://api.github.com/repos/apache/synapse/deployments",
          "created_at": "2009-05-21T01:01:59Z",
          "updated_at": "2022-11-22T21:17:50Z",
          "pushed_at": "2023-02-16T13:01:57Z",
          "git_url": "git://github.com/apache/synapse.git",
          "ssh_url": "**************:apache/synapse.git",
          "clone_url": "https://github.com/apache/synapse.git",
          "svn_url": "https://github.com/apache/synapse",
          "homepage": "",
          "size": 45172,
          "stargazers_count": 48,
          "watchers_count": 48,
          "language": "Java",
          "has_issues": false,
          "has_projects": true,
          "has_downloads": true,
          "has_wiki": false,
          "has_pages": false,
          "has_discussions": false,
          "forks_count": 53,
          "mirror_url": null,
          "archived": false,
          "disabled": false,
          "open_issues_count": 12,
          "license": null,
          "allow_forking": true,
          "is_template": false,
          "web_commit_signoff_required": false,
          "topics": [
            "http",
            "java",
            "network-client",
            "network-server",
            "synapse",
            "xml"
          ],
          "visibility": "public",
          "forks": 53,
          "open_issues": 12,
          "watchers": 48,
          "default_branch": "master",
          "permissions": {
            "admin": false,
            "maintain": false,
            "push": false,
            "triage": false,
            "pull": true
          }
        }
      ]
    }
  },
  {
    "httpRequest": {
      "method": "GET",
      "path": "/api/v4/projects"
    },
    "httpResponse": {
      "body": [
        {
          "id": 41182117,
          "description": "first project",
          "name": "HTML and CSS exploration",
          "name_with_namespace": "Isaac / HTML and CSS exploration",
          "path": "html-and-css-exploration",
          "http_url_to_repo": "https://gitlab.com/kttkpm_nhom2/apigatewayservice.git"
        }
      ]
    }
  },
  {
    "httpRequest": {
      "method" : "GET",
      "path": "/rest/api/3/search"
    },
    "httpResponse": {
      "body": [
        {
          "expand": "schema,names",
          "startAt": 0,
          "maxResults": 50,
          "total": 3
        }
      ]
    }
  },
  {
    "httpRequest": {
      "method" : "GET",
      "path": "/jsonpath/mock"
    },
    "httpResponse": {
      "body": {
        "store": {
          "book": [
            {
              "category": "reference",
              "author": "Nigel Rees",
              "title": "Sayings of the Century",
              "price": 8.95
            },
            {
              "category": "fiction",
              "author": "Evelyn Waugh",
              "title": "Sword of Honour",
              "price": 12.99
            },
            {
              "category": "fiction",
              "author": "Herman Melville",
              "title": "Moby Dick",
              "isbn": "0-553-21311-3",
              "price": 8.99
            },
            {
              "category": "fiction",
              "author": "J. R. R. Tolkien",
              "title": "The Lord of the Rings",
              "isbn": "0-395-19395-8",
              "price": 22.99
            }
          ],
          "bicycle": {
            "color": "red",
            "price": 19.95
          }
        },
        "expensive": 10
      },
      "headers": {
        "Content-Type": "application/json"
      }
    }
  },
  {
    "httpRequest": {
      "method" : "GET",
      "path": "/api/lists"
    },
    "httpResponse": {
      "body": [
        {
          "type": "list",
          "id": "RHHK8D",
          "attributes": {
            "name": "SMS Subscribers",
            "created": "2021-10-30T12:07:31+00:00",
            "updated": "2021-10-30T12:07:31+00:00"
          },
          "links": {
            "self": "https://a.klaviyo.com/api/lists/RHHL8D/"
          }
        },
        {
          "type": "list",
          "id": "VtQ3Tp",
          "attributes": {
            "name": "Preview List",
            "created": "2022-10-30T12:07:29+00:00",
            "updated": "2022-10-30T12:07:29+00:00"
          },
          "links": {
            "self": "https://a.klaviyo.com/api/lists/VtQ3Qp/"
          }
        },
        {
          "type": "list",
          "id": "XTgXXv",
          "attributes": {
            "name": "Newsletter",
            "created": "2022-10-30T12:07:29+00:00",
            "updated": "2022-10-30T12:07:29+00:00"
          },
          "links": {
            "self": "https://a.klaviyo.com/api/lists/XTgXXv/"
          }
        },
        {
          "type": "list",
          "id": "UHIG4F",
          "attributes": {
            "name": "TestList",
            "created": "2021-10-30T16:07:29+00:00",
            "updated": "2021-10-30T16:36:29+00:00"
          },
          "links": {
            "self": "https://a.klaviyo.com/api/lists/UHIG4F/"
          }
        },
        {
          "type": "list",
          "id": "HLREBM",
          "attributes": {
            "name": "NewList",
            "created": "2022-08-30T12:07:29+00:00",
            "updated": "2022-08-30T12:07:29+00:00"
          },
          "links": {
            "self": "https://a.klaviyo.com/api/lists/HLREBM/"
          }
        }
      ]
    }
  },
  {
    "httpRequest": {
      "method" : "GET",
      "path": "/api/team"
    },
    "httpResponse": {
      "body": [
        {
          "_id": "tea_aaqam5a3BkY8aje24",
          "name": "PiedPiper",
          "userIds": ["usr_45y54yug42yh4h66j7j"],
          "createdBy": "usr_45y54yug42yh4h66j7j",
          "createdAt": "2018-04-30T12:19:42.829Z",
          "apiKey": "aa13722b45b9c475cc686231b1af6583",
          "billing": {
            "quantity": 1,
            "ok": true,
            "plan": "freetrial"
          }
        },
        {
          "_id": "tea_abt325f32332aje4364",
          "name": "TaoZex",
          "userIds": ["usr_gh954gbiu5bg4t5l54t43t"],
          "createdBy": "gh954gbiu5bg4t5l54t43t",
          "createdAt": "2018-07-30T12:19:42.829Z",
          "apiKey": "y4vu3yf74g3b4o3878438f4837fg4g48",
          "billing": {
            "quantity": 2,
            "ok": true,
            "plan": "sport"
          }
        },
        {
          "_id": "tea_fta8f7tas68fgsf6as",
          "name": "Frivlc",
          "userIds": ["usr_r3w4t5y6h65u79f8ehfe6"],
          "createdBy": "r3w4t5y6h65u79f8ehfe6",
          "createdAt": "2021-04-30T12:19:42.829Z",
          "apiKey": "8fh9473gfo847hf874bhwf76h4uifh44",
          "billing": {
            "quantity": 1,
            "ok": false,
            "plan": "test"
          }
        },
        {
          "_id": "tea_6sdtgsrgdsghirq32r",
          "name": "Lisa",
          "userIds": ["usr_84974h3fyg453u5tkg"],
          "createdBy": "usr_84974h3fyg453u5tkg",
          "createdAt": "2018-04-30T12:26:42.829Z",
          "apiKey": "7gf987f756agff7uagfo87agf8oaf3",
          "billing": {
            "quantity": 0,
            "ok": true,
            "plan": "study"
          }
        },
        {
          "_id": "tea_78yhwg7e5rsyges8o7g",
          "name": "Jack",
          "userIds": ["usr_768h3fyv34i7w6g4y4fw"],
          "createdBy": "usr_768h3fyv34i7w6g4y4fw",
          "createdAt": "2019-04-30T11:19:42.829Z",
          "apiKey": "3kruy4v3ir764gklrug4kw7rw4li784",
          "billing": {
            "quantity": 2,
            "ok": false,
            "plan": "eat food"
          }
        }
      ]
    }
  },
  {
    "httpRequest": {
      "method" : "GET",
      "path": "/api/campaigns"
    },
    "httpResponse": {
      "body": [
        {
          "_id":"bydeV",
          "name":"TaoZex's campaign 1"
        },
        {
          "_id":"wIIdk",
          "name":"TaoZex's campaign 2"
        },
        {
          "_id":"qvHRy",
          "name":"TaoZex's campaign 3"
        },
        {
          "_id":"WfBPo",
          "name":"TaoZex's campaign 4"
        },
        {
          "_id":"yPhVF",
          "name":"TaoZex's campaign 5"
        }
      ]
    }
  },
  {
    "httpRequest": {
      "method" : "GET",
      "path": "/v1/users"
    },
    "httpResponse": {
      "body":
      {
        "results": [
          {
            "object": "user",
            "id": "d40e767c-d7af-4b18-a86d-55c61f1e39a4",
            "type": "person",
            "person": {
              "email": "<EMAIL>"
            },
            "name": "Avocado Lovelace",
            "avatar_url": "https://secure.notion-static.com/d40e767c-d7af-4b18-a86d-55c61f1e39a4.jpg"
          },
          {
            "object": "user",
            "id": "8151f1be-63f2-4c8c-9348-7ad6fda73b3d",
            "type": "person",
            "person": {
              "email": "<EMAIL>"
            },
            "name": "TaoZex",
            "avatar_url": "https://secure.notion-static.com/8151f1be-63f2-4c8c-9348-7ad6fda73b3d.jpg"
          },
          {
            "object": "user",
            "id": "6386d2ea-d98f-468b-8f01-116d920a1e42",
            "type": "person",
            "person": {
              "email": "<EMAIL>"
            },
            "name": "test",
            "avatar_url": "https://secure.notion-static.com/6386d2ea-d98f-468b-8f01-116d920a1e42.jpg"
          },
          {
            "object": "user",
            "id": "63844g53-d98f-444g-8f01-116344g0a1e42",
            "type": "person",
            "person": {
              "email": "<EMAIL>"
            },
            "name": "Jack",
            "avatar_url": "https://secure.notion-static.com/63844g53-d98f-444g-8f01-116344g0a1e42.jpg"
          },
          {
            "object": "user",
            "id": "5786d2ea-d95f-468b-8361-11643t6551r45",
            "type": "person",
            "person": {
              "email": "<EMAIL>"
            },
            "name": "Lisa",
            "avatar_url": "https://secure.notion-static.com/5786d2ea-d95f-468b-8361-11643t6551r45.jpg"
          }
        ],
        "next_cursor": "fe2cc560-036c-44cd-90e8-294d5a74cebc",
        "has_more": true
      }
    }
  },
  {
    "httpRequest": {
      "method" : "GET",
      "path": "/api/v1/apps"
    },
    "httpResponse": {
      "body": [
        {
          "id": "8151f1be-63f2-4c8c-9348-7ad6fda73b3d",
          "name": "enjoy life",
          "gcm_key": null,
          "chrome_key": null,
          "chrome_web_key": null,
          "chrome_web_origin": null,
          "chrome_web_gcm_sender_id": null,
          "chrome_web_default_notification_icon": null,
          "chrome_web_sub_domain": null,
          "apns_env": null,
          "apns_certificates": null,
          "apns_p8": null,
          "apns_team_id": null,
          "apns_key_id": null,
          "apns_bundle_id": null,
          "safari_apns_certificate": null,
          "safari_site_origin": null,
          "safari_push_id": null,
          "safari_icon_16_16": "public/safari_packages/8151f1be-63f2-4c8c-9348-7ad6fda73b3d/icons/16x16.png",
          "safari_icon_32_32": "public/safari_packages/8151f1be-63f2-4c8c-9348-7ad6fda73b3d/icons/<EMAIL>",
          "safari_icon_64_64": "public/safari_packages/8151f1be-63f2-4c8c-9348-7ad6fda73b3d/icons/<EMAIL>",
          "safari_icon_128_128": "public/safari_packages/8151f1be-63f2-4c8c-9348-7ad6fda73b3d/icons/128x128.png",
          "safari_icon_256_256": "public/safari_packages/8151f1be-63f2-4c8c-9348-7ad6fda73b3d/icons/<EMAIL>",
          "site_name": null,
          "created_at": "2022-10-30T14:48:14.688Z",
          "updated_at": "2022-10-30T14:48:14.953Z",
          "players": 100,
          "messageable_players": 0,
          "basic_auth_key": "Y2EyZjI5NzgtMzU1NC00NTU3LWIwNWItXGQ0MzQ4MzQ2ZjY2",
          "additional_data_is_root_payload": false
        },
        {
          "id": "6386d2ea-d98f-468b-8f01-116d920a1e42",
          "name": "test",
          "gcm_key": null,
          "chrome_key": null,
          "chrome_web_key": null,
          "chrome_web_origin": null,
          "chrome_web_gcm_sender_id": null,
          "chrome_web_default_notification_icon": null,
          "chrome_web_sub_domain": null,
          "apns_env": null,
          "apns_certificates": null,
          "apns_p8": null,
          "apns_team_id": null,
          "apns_key_id": null,
          "apns_bundle_id": null,
          "safari_apns_certificate": null,
          "safari_site_origin": null,
          "safari_push_id": null,
          "safari_icon_16_16": "public/safari_packages/6386d2ea-d98f-468b-8f01-116d920a1e42/icons/16x16.png",
          "safari_icon_32_32": "public/safari_packages/6386d2ea-d98f-468b-8f01-116d920a1e42/icons/<EMAIL>",
          "safari_icon_64_64": "public/safari_packages/6386d2ea-d98f-468b-8f01-116d920a1e42/icons/<EMAIL>",
          "safari_icon_128_128": "public/safari_packages/6386d2ea-d98f-468b-8f01-116d920a1e42/icons/128x128.png",
          "safari_icon_256_256": "public/safari_packages/6386d2ea-d98f-468b-8f01-116d920a1e42/icons/<EMAIL>",
          "site_name": null,
          "created_at": "2022-10-30T14:50:24.711Z",
          "updated_at": "2022-10-30T14:50:24.849Z",
          "players": 1,
          "messageable_players": 0,
          "basic_auth_key": "ODFiZWJiZDItZWYwZC00ODYzLWE4YmUtYTRmY2ZjNTU5NTVi",
          "additional_data_is_root_payload": false
        },
        {
          "id": "63844g53-d98f-468b-8f01-11632320a1e42",
          "name": "game",
          "gcm_key": null,
          "chrome_key": null,
          "chrome_web_key": null,
          "chrome_web_origin": null,
          "chrome_web_gcm_sender_id": null,
          "chrome_web_default_notification_icon": null,
          "chrome_web_sub_domain": null,
          "apns_env": null,
          "apns_certificates": null,
          "apns_p8": null,
          "apns_team_id": null,
          "apns_key_id": null,
          "apns_bundle_id": null,
          "safari_apns_certificate": null,
          "safari_site_origin": null,
          "safari_push_id": null,
          "safari_icon_16_16": "public/safari_packages/63844g53-d98f-468b-8f01-11632320a1e42/icons/16x16.png",
          "safari_icon_32_32": "public/safari_packages/63844g53-d98f-468b-8f01-11632320a1e42/icons/<EMAIL>",
          "safari_icon_64_64": "public/safari_packages/63844g53-d98f-468b-8f01-11632320a1e42/icons/<EMAIL>",
          "safari_icon_128_128": "public/safari_packages/63844g53-d98f-468b-8f01-11632320a1e42/icons/128x128.png",
          "safari_icon_256_256": "public/safari_packages/63844g53-d98f-468b-8f01-11632320a1e42/icons/<EMAIL>",
          "site_name": null,
          "created_at": "2022-10-30T14:50:24.711Z",
          "updated_at": "2022-10-30T14:50:24.849Z",
          "players": 16,
          "messageable_players": 0,
          "basic_auth_key": "ODFiZWJiZDItZWYwZC00ODYzLWE4MmUtYTRmY2ZjNTU5NTVi",
          "additional_data_is_root_payload": false
        },
        {
          "id": "632332a-d9f-43238b-8f2301-11a1e42",
          "name": "metting",
          "gcm_key": null,
          "chrome_key": null,
          "chrome_web_key": null,
          "chrome_web_origin": null,
          "chrome_web_gcm_sender_id": null,
          "chrome_web_default_notification_icon": null,
          "chrome_web_sub_domain": null,
          "apns_env": null,
          "apns_certificates": null,
          "apns_p8": null,
          "apns_team_id": null,
          "apns_key_id": null,
          "apns_bundle_id": null,
          "safari_apns_certificate": null,
          "safari_site_origin": null,
          "safari_push_id": null,
          "safari_icon_16_16": "public/safari_packages/632332a-d9f-43238b-8f2301-11a1e42/icons/16x16.png",
          "safari_icon_32_32": "public/safari_packages/632332a-d9f-43238b-8f2301-11a1e42/icons/<EMAIL>",
          "safari_icon_64_64": "public/safari_packages/632332a-d9f-43238b-8f2301-11a1e42/icons/<EMAIL>",
          "safari_icon_128_128": "public/safari_packages/632332a-d9f-43238b-8f2301-11a1e42/icons/128x128.png",
          "safari_icon_256_256": "public/safari_packages/632332a-d9f-43238b-8f2301-11a1e42/icons/<EMAIL>",
          "site_name": null,
          "created_at": "2022-10-30T14:50:24.711Z",
          "updated_at": "2022-10-30T14:50:24.849Z",
          "players": 0,
          "messageable_players": 0,
          "basic_auth_key": "ODFiZWJiZDItZWYwZgbvODYzLWE4MmUtYTRmY2ZjNTU5NTVi",
          "additional_data_is_root_payload": false
        },
        {
          "id": "6386d2ea-d98f-468b-8f01-116d23r0a1e42",
          "name": "app test",
          "gcm_key": null,
          "chrome_key": null,
          "chrome_web_key": null,
          "chrome_web_origin": null,
          "chrome_web_gcm_sender_id": null,
          "chrome_web_default_notification_icon": null,
          "chrome_web_sub_domain": null,
          "apns_env": null,
          "apns_certificates": null,
          "apns_p8": null,
          "apns_team_id": null,
          "apns_key_id": null,
          "apns_bundle_id": null,
          "safari_apns_certificate": null,
          "safari_site_origin": null,
          "safari_push_id": null,
          "safari_icon_16_16": "public/safari_packages/6386d2ea-d98f-468b-8f01-116d23r0a1e42/icons/16x16.png",
          "safari_icon_32_32": "public/safari_packages/6386d2ea-d98f-468b-8f01-116d23r0a1e42/icons/<EMAIL>",
          "safari_icon_64_64": "public/safari_packages/6386d2ea-d98f-468b-8f01-116d23r0a1e42/icons/<EMAIL>",
          "safari_icon_128_128": "public/safari_packages/6386d2ea-d98f-468b-8f01-116d23r0a1e42/icons/128x128.png",
          "safari_icon_256_256": "public/safari_packages/6386d2ea-d98f-468b-8f01-116d23r0a1e42/icons/<EMAIL>",
          "site_name": null,
          "created_at": "2022-10-30T14:50:24.711Z",
          "updated_at": "2022-10-30T14:50:24.849Z",
          "players": 2,
          "messageable_players": 0,
          "basic_auth_key": "ODFiZWJiZDItZWYwZC00ODYzLWE4MmUtYgreY2ZjNTU5NTVi",
          "additional_data_is_root_payload": false
        }
      ]
    }
  },
  {
    "httpRequest": {
      "method" : "GET",
      "path": "/persistiq/v1/users"
    },
    "httpResponse": {
      "body":
      {
        "status": "success",
        "errors": [],
        "users": [
          {
            "id": "u_q3e537",
            "name": "Tiana Eichmann MD",
            "email": "<EMAIL>",
            "activated": true,
            "default_mailbox_id": "mbox_...",
            "salesforce_id": null
          },
          {
            "id": "u_2ljD34",
            "name": "Brendan Reichert",
            "email": "<EMAIL>",
            "activated": true,
            "default_mailbox_id": "mbox_...",
            "salesforce_id": null
          },
          {
            "id": "u_M3kXp2",
            "name": "Chester Lind",
            "email": "<EMAIL>",
            "activated": false,
            "default_mailbox_id": "mbox_...",
            "salesforce_id": null
          },
          {
            "id": "u_114g0a",
            "name": "TaoZex",
            "email": "<EMAIL>",
            "activated": true,
            "default_mailbox_id": "mbox_...",
            "salesforce_id": null
          },
          {
            "id": "u_h44g53",
            "name": "Jack",
            "email": "<EMAIL>",
            "activated": false,
            "default_mailbox_id": "mbox_...",
            "salesforce_id": null
          }
        ]
      }
    }
  },
  {
    "httpRequest": {
      "method" : "POST",
      "path": "/example/jsonBody",
      "body": {
        "type": "JSON",
        "json": {
          "id": 1
        },
        "matchType": "STRICT"
      }
    },
    "httpResponse": {
      "body": [
        {
          "name": "lzl",
          "age": 18
        },
        {
          "name": "pizz",
          "age": 19
        }
      ],
      "headers": {
        "Content-Type": "application/json"
      }
    }
  },
  {
    "httpRequest": {
      "path": "/example/formBody",
      "method": "POST",
      "body": {
        "type": "PARAMETERS",
        "parameters": {
          "id": "1"
        }
      }
    },
    "httpResponse": {
      "body": [
        {
          "name": "lzl",
          "age": 18
        },
        {
          "name": "pizz",
          "age": 19
        }
      ],
      "headers": {
        "Content-Type": "application/json"
      }
    }
  },
  {
    "httpRequest": {
      "path": "/example/webhook",
      "method": "POST",
      "headers": {
        "token": ["9e32e859ef044462a257e1fc76730066"]
      }
    },
    "httpResponse": {
      "body": [
        {
          "name": "lzl",
          "age": 18
        },
        {
          "name": "pizz",
          "age": 19
        }
      ],
      "headers": {
        "Content-Type": "application/json"
      }
    }
  },
  {
    "httpRequest": {
      "method" : "GET",
      "path": "/query/pages",
      "queryStringParameters": {
        "page": "1"
      }
    },
    "httpResponse": {
      "body":
      {
        "status": null,
        "msg": null,
        "data": [
          {
            "name": "name1",
            "age": 69
          },
          {
            "name": "name2",
            "age": 51
          }
        ],
        "currentPageIndex": 1,
        "totalPage": 2
      }
    }
  },
  {
    "httpRequest": {
      "method" : "GET",
      "path": "/query/pages",
      "queryStringParameters": {
        "page": "2"
      }
    },
    "httpResponse": {
      "body":
      {
        "status": null,
        "msg": null,
        "data": [
          {
            "name": "name1",
            "age": 69
          },
          {
            "name": "name2",
            "age": 51
          }
        ],
        "currentPageIndex": 2,
        "totalPage": 2
      }
    }
  },
  {
    "httpRequest": {
      "method" : "GET",
      "path": "/query/pagesNoPageNum",
      "queryStringParameters": {
        "page": "1"
      }
    },
    "httpResponse": {
      "body":
      {
        "status": null,
        "msg": null,
        "data": [
          {
            "name": "name1",
            "age": 69
          },
          {
            "name": "name2",
            "age": 51
          },
          {
            "name": "name3",
            "age": 36
          },
          {
            "name": "name4",
            "age": 51
          },
          {
            "name": "name5",
            "age": 74
          },
          {
            "name": "name6",
            "age": 51
          },
          {
            "name": "name7",
            "age": 67
          },
          {
            "name": "name8",
            "age": 12
          },
          {
            "name": "name9",
            "age": 45
          },
          {
            "name": "name10",
            "age": 23
          }
        ],
        "currentPageIndex": 1,
        "hasNext": true
      }
    }
  },
  {
    "httpRequest": {
      "method" : "GET",
      "path": "/query/pagesNoPageNum",
      "queryStringParameters": {
        "page": "2"
      }
    },
    "httpResponse": {
      "body":
      {
        "status": null,
        "msg": null,
        "data": [
          {
            "name": "name11",
            "age": 69
          },
          {
            "name": "name22",
            "age": 51
          }
        ],
        "currentPageIndex": 2,
        "hasNext": false
      }
    }
  },
  {
    "httpRequest": {
      "path": "/example/feishu/108bb8f208d9b2378c8c7aedad715c19",
      "method": "POST"
    },
    "httpResponse": {
      "body": [
        {
          "name": "lzl",
          "age": 18
        },
        {
          "name": "pizz",
          "age": 19
        }
      ],
      "headers": {
        "Content-Type": "application/json"
      }
    }
  },
  {
    "httpRequest": {
      "path": "/example/httpContentSink",
      "method": "POST",
      "headers": {
        "token": ["9e32e859ef044462a257e1fc76730066"]
      },
      "body": {
        "type": "JSON",
        "json": {"content" : "[ {\n  \"name\" : \"lzl\",\n  \"age\" : 18\n}, {\n  \"name\" : \"pizz\",\n  \"age\" : 19\n} ]"},
        "matchType": "STRICT"
      }
    },
    "httpResponse": {
      "body": [
        {
          "name": "lzl2",
          "age": 18
        },
        {
          "name": "pizz2",
          "age": 19
        }
      ],
      "headers": {
        "Content-Type": "application/json"
      }
    }
  }
]
