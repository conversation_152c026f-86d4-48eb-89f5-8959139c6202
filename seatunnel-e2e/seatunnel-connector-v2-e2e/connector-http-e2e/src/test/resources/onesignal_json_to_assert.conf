#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  job.mode = "BATCH"
}

source {
  OneSignal {
    result_table_name = "http"
    url = "http://mockserver:1080/api/v1/apps"
    password = "SeaTunnel-test"
    method = "GET"
    format = "json"
    schema = {
      fields {
        id = string
        name = string
        gcm_key = string
        chrome_key = string
        chrome_web_key = string
        chrome_web_origin = string
        chrome_web_gcm_sender_id = string
        chrome_web_default_notification_icon = string
        chrome_web_sub_domain = string
        apns_env = string
        apns_certificates = string
        apns_p8 = string
        apns_team_id = string
        apns_key_id = string
        apns_bundle_id = string
        safari_apns_certificate = string
        safari_site_origin = string
        safari_push_id = string
        safari_icon_16_16 = string
        safari_icon_32_32 = string
        safari_icon_64_64 = string
        safari_icon_128_128 = string
        safari_icon_256_256 = string
        site_name = string
        created_at = string
        updated_at = string
        players = int
        messageable_players = int
        basic_auth_key = string
        additional_data_is_root_payload = string
      }
    }
  }
}

sink {
  Assert {
    source_table_name = "http"
    rules {
      row_rules = [
        {
          rule_type = MAX_ROW
          rule_value = 5
        },
        {
          rule_type = MIN_ROW
          rule_value = 5
        }
      ],

      field_rules = [
        {
          field_name = id
          field_type = string
          field_value = [
            {
              rule_type = NOT_NULL
            }
          ]
        },
        {
          field_name = name
          field_type = string
          field_value = [
            {
              rule_type = NOT_NULL
            }
          ]
        },
        {
          field_name = players
          field_type = int
          field_value = [
            {
              rule_type = NOT_NULL
            }
          ]
        }
      ]
    }
  }
}
