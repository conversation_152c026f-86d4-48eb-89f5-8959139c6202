#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  job.mode = "${dynamic_job_mode}" # dynamic_job_mode will be replace to the final file name before test run
  checkpoint.interval = 5000
}

source {
  # This is a example source plugin **only for test and demonstrate the feature source plugin**
  FakeSource {
    result_table_name = table1
    row.num = ${dynamic_test_row_num_per_parallelism}
    split.num = 5
    split.read-interval = 3000
    map.size = 10
    array.size = 10
    bytes.length = 10
    string.length = 10
    parallelism = ${dynamic_test_parallelism}
    schema = {
      fields {
        c_map = "map<string, array<int>>"
        c_array = "array<int>"
        c_string = string
        c_boolean = boolean
        c_tinyint = tinyint
        c_smallint = smallint
        c_int = int
        c_bigint = bigint
        c_float = float
        c_double = double
        c_decimal = "decimal(30, 8)"
        c_null = "null"
        c_bytes = bytes
        c_date = date
        c_timestamp = timestamp
        c_row = {
          c_map = "map<string, map<string, string>>"
          c_array = "array<int>"
          c_string = string
          c_boolean = boolean
          c_tinyint = tinyint
          c_smallint = smallint
          c_int = int
          c_bigint = bigint
          c_float = float
          c_double = double
          c_decimal = "decimal(30, 8)"
          c_null = "null"
          c_bytes = bytes
          c_date = date
          c_timestamp = timestamp
        }
      }
    }
  }

  FakeSource {
    result_table_name = table2
    row.num = ${dynamic_test_row_num_per_parallelism}
    split.num = 5
    split.read-interval = 3000
    map.size = 10
    array.size = 10
    bytes.length = 10
    string.length = 10
    parallelism = ${dynamic_test_parallelism}
    schema = {
      fields {
        c_map = "map<string, array<int>>"
        c_array = "array<int>"
        c_string = string
        c_boolean = boolean
        c_tinyint = tinyint
        c_smallint = smallint
        c_int = int
        c_bigint = bigint
        c_float = float
        c_double = double
        c_decimal = "decimal(30, 8)"
        c_null = "null"
        c_bytes = bytes
        c_date = date
        c_timestamp = timestamp
        c_row = {
          c_map = "map<string, map<string, string>>"
          c_array = "array<int>"
          c_string = string
          c_boolean = boolean
          c_tinyint = tinyint
          c_smallint = smallint
          c_int = int
          c_bigint = bigint
          c_float = float
          c_double = double
          c_decimal = "decimal(30, 8)"
          c_null = "null"
          c_bytes = bytes
          c_date = date
          c_timestamp = timestamp
        }
      }
    }
  }
}

transform {
}

sink {
  LocalFile {
    source_table_name = table1
    path = "/tmp/hive/warehouse/${dynamic_test_case_name}" # dynamic_test_case_name will be replace to the final file name before test run
    field_delimiter = "\t"
    row_delimiter = "\n"
    file_name_expression = "${transactionId}_${now}"
    file_format_type = "text"
    filename_time_format = "yyyy.MM.dd"
    is_enable_transaction = true
    save_mode = "error"
  }

  LocalFile {
    source_table_name = table2
    path = "/tmp/hive/warehouse/${dynamic_test_case_name}" # dynamic_test_case_name will be replace to the final file name before test run
    field_delimiter = "\t"
    row_delimiter = "\n"
    file_name_expression = "${transactionId}_${now}"
    file_format_type = "text"
    filename_time_format = "yyyy.MM.dd"
    is_enable_transaction = true
    save_mode = "error"
  }
}
