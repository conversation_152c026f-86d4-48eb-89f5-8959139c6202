#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

seatunnel:
  engine:
    history-job-expire-minutes: 1
    backup-count: 2
    queue-type: blockingqueue
    print-execution-info-interval: 10
    slot-service:
      dynamic-slot: true
    checkpoint:
      interval: 300000
      timeout: 100000
      storage:
        type: localfile
        max-retained: 3
        plugin-config:
          namespace: /tmp/seatunnel/checkpoint_snapshot/
    jar-storage:
      enable: true
      connector-jar-storage-mode: SHARED
      connector-jar-storage-path: ""
      connector-jar-cleanup-task-interval: 3600
      connector-jar-expiry-time: 600
