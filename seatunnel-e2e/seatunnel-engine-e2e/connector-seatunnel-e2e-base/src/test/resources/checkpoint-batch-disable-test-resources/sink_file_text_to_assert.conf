#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  job.name = "DISABLE_CHECKPOINT_ASSERT"
  job.mode = "BATCH"
}

source {
  LocalFile {
    path = "/tmp/seatunnel/config/checkpoint-batch-disable-test-resources/sinkfile"
    file_format_type = "text"
    schema = {
      fields {
        c_string = string
      }
    }
    result_table_name = "fake"
  }
}

sink {
  Assert {
    rules {
      row_rules = [
        {
          rule_type = MAX_ROW
          rule_value = 100
        },
        {
          rule_type = MIN_ROW
          rule_value = 100
        }
      ]
    }
  }
}