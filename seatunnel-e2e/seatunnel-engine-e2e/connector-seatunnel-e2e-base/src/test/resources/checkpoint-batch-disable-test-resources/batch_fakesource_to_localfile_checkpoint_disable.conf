#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of disabled checkpoint in batch mode
######

env {
  parallelism = 1
  job.mode = "BATCH"
  job.name = "DISABLE_CHECKPOINT"

  # You can set spark configuration here
  spark.executor.instances = 2
  spark.executor.cores = 1
  spark.executor.memory = "1g"
  spark.master = local
}

source {
  # This is a example source plugin **only for test and demonstrate the feature source plugin**
  FakeSource {
    result_table_name = "fake"
    row.num = 100
    split.num = 5
    split.read-interval = 3000
    schema = {
      fields {
        id = "int"
        name = "string"
        age = "int"
      }
    }
  }
}
transform {
}
sink {
  LocalFile {
    path = "/tmp/seatunnel/config/checkpoint-batch-disable-test-resources/sinkfile/"
    row_delimiter = "\n"
    file_name_expression = "${transactionId}_${now}"
    file_format_type = "text"
    filename_time_format = "yyyy.MM.dd"
    is_enable_transaction = true
  }
}